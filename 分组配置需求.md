# 分组配置算子页面逻辑修正说明

## 需求描述

用户需求：在 `groupSumSetting.vue` 中，原配置页面![alt text](image.png)，现把配置页面修改为![alt text](image-1.png)。
具体修改逻辑如下：
（1）节点名称，节点Id 处理逻辑保持不变。
（2）增加分组类型下拉框，共用2个选项，分别是周期迭代计算、时间分组聚合。
（3）如果选择 周期迭代计算，那么就跳转到时间周期组件进行时间周期连接，TimeFlowChart.vue. 计算字段选项跟之前逻辑保持一致。
    常见指标（可多选）枚举格式如下，
    MIN(4, "vMin", "最小值", "double"),
    MAX(3, "vMax", "最大值", "double"),
    AVG(11, "vAvg", "平均值", "double"),
    SUM(10, "vSum", "累加值", "double"),
    ARITHSUM(18, "vArithSum", "算术累加", "double"),
    CP95(6, "vCp95", "95百分位值", "double"),
    COUNT(17, "vCount", "计数", "double"),
    QUALIFICATION(12, "vQualificationRate", "合格率", "double");

    下拉框中显示 最小值、最大值、平均值、累加值、算术累加、95百分位值、计数、合格率
    选中后，页面显示对应的中文名称，以数组的方式传输 例如：[MIN(4, "vMin", "最小值", "double")].

    最下面是自定义指标，其中 指标名称、标识、指标公式 是用户在页面可以直接输入。操作里面包含 编辑和删除按钮，分别是编辑指标名称、标识、指标公式这3个指标和删除这行。上面有新增按钮，增加一行指标。
    自定义指标输出格式如下：
    #公式定义
        "formulas":[
          {
            "name":"不平衡度",
            "label":"unbalanceT4",
            "formula": "(max()-min())/max()"
          },
          {
            "name":"日均负载率",
            "label":"dailyLoadFactorT4",
            "formula": "sqrt(pow(x1,2) + pow(x2,2))/${capacity} * 24"
          },
          {
            "name":"月均负载率",
            "label":"dailyLoadFactorT4",
            "formula": "sqrt(pow(x1,2) + pow(x2,2))/${capacity} * month_days(time)"
          },
          {
            "name":"覆盖率",
            "label":"dataCoverageRateT4",
            "formula": "count() * ${interval}/period_minutes(time)"
          }
        ]

（4）如果选择 时间分组聚合，那么就显示 原页面的分组模式、分组字段、计算字段等配置项，跟之前逻辑保持一致。常见指标，自定义指标跟（3）中常见指标和自定义指标处理逻辑一致。
    



