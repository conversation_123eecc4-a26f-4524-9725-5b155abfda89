import { defineStore } from "pinia";
// id: 必须唯一
export const useflowChartStore = defineStore("flow-chart", () => {
  // state
  const edgeType = ref<string>("step");

  // 页面步骤条当前步骤
  const pageStep = ref<number>(0);

  // 业务维度配置算子输入源选择
  const inputSource: InputModelParam = {};

  // 删除节点所对应的字段节点算子
  let deleteNodeFiled: NodeClearMapParam = {};

  // 所有全局计算属性参数保存
  const arguesData = ref<GlobalCalculationParam[]>([
    {
      resource: {},
      source: [],
      trans: [],
      sink: [],
    },
  ]);

  // 流程设计全局中的流程备注文本信息保存
  const flowRemark = ref<string>("");

  // 输入算子数据保存
  const inputOperatorData = ref<AllInputParam[]>([]);

  // 临时存储单个数组数据
  const tempInputData = ref<AllInputParam>();

  // 输出算子数据保存
  const outputOperatorData = ref<AllOutputParam[]>([]);

  // 临时存储单个数组数据
  const tempOutputData = ref<AllOutputParam>();

  // 所有计算属性算子数据保存
  const computerOperatorData = ref<EditDrawerParam[]>([]);

  // 临时存储单个数组数据
  const tempComputerData = ref<EditDrawerParam>();

  // actions
  function updateEdgeType(type: string) {
    edgeType.value = type;
  }

  function updateInputSource(id: string, data: ModelsParam) {
    inputSource[id] = data;
  }

  function updateResource(param: ResourceParam) {
    arguesData.value[0].resource = param;
    updateRemark(param);
  }

  // 数据回显
  function getInitArguesData(data: GlobalCalculationParam[]) {
    arguesData.value = data;
    inputOperatorData.value = data[0].source;
    outputOperatorData.value = data[0].sink;
    computerOperatorData.value = data[0].trans;
    updateSourceData(inputOperatorData.value);
    updateRemark(data[0].resource);
  }

  // 更新业务维度输入源
  function updateSourceData(data: AllInputParam[]) {
    data?.forEach((item) => {
      const { type, id, modelLabel } = item;
      if (type === "CfgSource") {
        inputSource[id] = { modelLabel: modelLabel ?? "" };
      }
    });
  }

  // 更新流程备注
  function updateRemark(param: ResourceParam) {
    flowRemark.value = param.remark || "";
  }

  // 更新计算属性算子数据
  function updateArguesData(
    type: string | undefined,
    oldData: EditDrawerParam[] | AllInputParam[] | AllOutputParam[]
  ) {
    switch (type) {
      case "source":
        arguesData.value[0].source = oldData;
        break;
      case "sink":
        arguesData.value[0].sink = oldData;
        break;
      default:
        arguesData.value[0].trans = oldData;
        break;
    }
  }
  // 更新单个数据
  function updateData(
    type: string | undefined,
    data: EditDrawerParam | AllInputParam | AllOutputParam
  ) {
    let oldData;
    switch (type) {
      case "source":
        oldData = inputOperatorData.value;
        break;
      case "sink":
        oldData = outputOperatorData.value;
        break;
      default:
        oldData = computerOperatorData.value;
        break;
    }
    const filterData = oldData.filter((item) => item.id === data.id);
    if (filterData.length > 0) {
      const index = oldData.indexOf(filterData[0]);
      oldData.splice(index, 1, data);
    } else {
      oldData.push(data);
    }
    updateArguesData(type, oldData);
  }
  // 更新临时数据
  function updateTempData(
    type: string | undefined,
    data: EditDrawerParam | AllInputParam | AllOutputParam
  ) {
    let oldData;
    switch (type) {
      case "source":
        oldData = tempInputData.value;
        break;
      case "sink":
        oldData = tempOutputData.value;
        break;
      default:
        oldData = tempComputerData.value;
        break;
    }
    const mergedObject = { ...oldData, ...data };
    switch (type) {
      case "source":
        tempInputData.value = mergedObject;
        break;
      case "sink":
        tempOutputData.value = mergedObject;
        break;
      default:
        tempComputerData.value = mergedObject;
        break;
    }
  }

  // 去除删除节点的数据
  function deleteData(
    type: string | undefined,
    id: string,
    isFirst: boolean = true
  ) {
    switch (type) {
      case "source":
        inputOperatorData.value = updateDeleteData(
          inputOperatorData.value,
          id,
          isFirst
        );
        updateArguesData(type, inputOperatorData.value);
        break;
      case "sink":
        outputOperatorData.value = updateDeleteData(
          outputOperatorData.value,
          id,
          isFirst
        );
        updateArguesData(type, outputOperatorData.value);
        break;
      default:
        computerOperatorData.value = updateDeleteData(
          computerOperatorData.value,
          id,
          isFirst
        );
        updateArguesData(type, computerOperatorData.value);
        break;
    }
  }

  function updateDeleteData(
    data: EditDrawerParam[] | AllInputParam[] | AllOutputParam[],
    id: string,
    isFirst: boolean = true
  ) {
    const filterData = data.filter((item) => item.id === id);
    if (filterData.length == 0) return data;
    const curData = filterData[0] as
      | AllInputParam
      | EditDrawerParam
      | AllOutputParam;
    const index = data.indexOf(curData);
    if (!isFirst && curData.type && deleteNodeFiled[curData.type]) {
      const arr = deleteNodeFiled[curData.type];
      arr.forEach((item) => {
        deleteProperty(curData, item as keyof typeof curData);
      });
      data.splice(index, 1, curData);
    }
    if (isFirst) {
      data.splice(index, 1);
    }
    return data;
  }

  function deleteProperty<T extends object>(obj: T, key: keyof T) {
    delete obj[key];
  }

  function getDeleteNodeFiled(val: NodeClearMapParam) {
    deleteNodeFiled = val;
  }

  // 清空所有临时数据
  function clearTempData() {
    tempInputData.value = undefined;
    tempOutputData.value = undefined;
    tempComputerData.value = undefined;
  }

  // 进入页面前清空所有算子数据
  function clearOperatorData() {
    inputOperatorData.value.length = 0;
    outputOperatorData.value.length = 0;
    computerOperatorData.value.length = 0;
  }

  // 更新当前步骤条
  function updatePageSteps(val: number) {
    pageStep.value = val;
  }

  // 更新全局流程备注信息
  function updateFlowRemark(val: string) {
    flowRemark.value = val;
    arguesData.value[0].resource.remark = val;
  }

  return {
    arguesData,
    getInitArguesData,
    edgeType,
    updateEdgeType,
    updateResource,
    computerOperatorData,
    updateData,
    inputOperatorData,
    outputOperatorData,
    updateTempData,
    tempInputData,
    tempOutputData,
    tempComputerData,
    clearTempData,
    inputSource,
    updateInputSource,
    deleteData,
    clearOperatorData,
    getDeleteNodeFiled,
    // 步骤条
    pageStep,
    updatePageSteps,
    // 备注
    flowRemark,
    updateFlowRemark,
  };
});
