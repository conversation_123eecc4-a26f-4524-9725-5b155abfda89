/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {};

/* prettier-ignore */
declare global {

  // 接口通用类型返回
  export interface CommonResponse {
    code: number;
    msg: string;
  }

  export interface CommonDataResponse<T> extends CommonResponse {
    data: T
  }

  // 接口通用类型返回
  export type PromisedCommonResp<T = unknown> = Promise<CommonDataResponse<T>>;

  export interface MenuListParam {
    label?: string;
  }
    
  // 新增节点定义
  export interface NewNode {
    id: string;
    type?: string;
    position: {x:number, y:number};
    data?: Data;
  }
    
  export interface Data {
    name: string;
    nodeType: string,
    type: string,
    dataStream?: string
  }

  // 流程列表--字段表格参数类型定义
  export interface TableParam {
    id: string | number;
    jobId?: string;
    recalculateId?: string; // 重算任务Id
    alias: string;
    processDesign?: string;
    jobName: string;
    administrator?: string;
    updateTime?: string;
    runningTime?: string; // 实时运行时间
    recalculateRunningTime?: string; // 重算运行时间
    operationMode?: string;
    status?: string; // 流程状态
    recalculateStatus?: string; // 重算状态
    remarks?: string;
    debugging?: string;
    argues?: string;
    // 用于判断运行按钮
    isRunning?: boolean;
    // 用于判断重算运行按钮
    isRecalculating?: boolean;
    // 重算历史参数
    lastRecalculateStartTime?: string;
    lastRecalculateVariables?: VariableParam[];
  }

  // 流程列表--列表数据参数类型定义
  export interface ProcessTableParam extends TableParam {
    children?: TableParam[];
  }

  // 抽屉左侧菜单参数类型定义
  export interface MenuParam {
    type?: string;
    subMenu?: MenuItemParam[];
  }

  export interface MenuItemParam {
    label: string;
    type?: string;
    key?: string;
    component?: any;
    icon?: any; // 这里假设 icon 是一个任意类型，具体类型取决于你的项目
    children?: MenuItemParam[]; // 子菜单项
  }

  // 自定义指标公式定义
  export interface FunctionsParam {
    func: string;
    funcField: string;
  }

  // 自定义指标公式定义
  export interface CustomFormulaParam {
    name: string;
    label: string;
    frontedType: string;
    fieldType: string;
    formula: string;
  }

  // 中间计算算子--弹窗信息类型定义
  export interface EditDrawerParam {
    id: string;
    name?: string;
    label?: string;
    nodeType?: string;
    dataStream?: string;
    to?: string;
    from?: string[];
    type?: string;
    fieldsArray?: string;
    // 分组汇总字段
    func?: string | number;
    locatedNode?: string;
    groupBy?: string[];
    funcField?: string;
    resultField?: string;
    periodField?: DateFieldParam[];
    periodEdges?: Array<{ start: number; end: number }>; // 时间节点连接关系
    totalRecordName?: string;
    pageCalculation?: string;
    remark?: string;
    joinTable?:FieldTableParam[];
    // 新增分组配置相关字段
    groupType?: string; // 分组类型：periodIteration 或 groupAggregation
    functions?: FunctionsParam[]; // 选中的常见指标
    formulas?: CustomFormulaParam[]; // 自定义指标公式
    // join连接字段
    joinType?: string;
    streamA?: string;
    streamAM?: string;
    streamB?: string;
    streamBM?: string;
    tableAField?: string;
    tableBField?: string;
    joinCondition?: string;
    associationTableData?: AssociationTableParam[],
    conditionRadio?: string;
    tableA?: FieldTableParam[];
    tableB?: FieldTableParam[];
    mergedTableField?: FieldTableParam[];
    // 筛选算子
    totalFilter?: string;
    filterCondition?: string;
    filterConditionArray?: FilterConditionArrayParam[],
    filterTable?: FieldTableParam[];
    // 字段设置算子
    fieldTable?: FieldTableParam[];
    allfieldTable?: FieldTableParam[];
    // 调试预览
    debugModel?: boolean;
    debugDataSource?: string;
    // 异常处理算子
    businessScene?: string;   // 业务场景字段
    dynamicField?: DynamicFieldParam;       // 业务场景中所有动态参数集合
    abnormalParam?: ExceptionParam[];      // 异常参数集合
    upstreamFieldTable?: FieldTableParam[];  // 上游字段表
  }

  // join连接算子--关联表格字段
  export interface AssociationTableParam {
    selectedAField: string;
    selectedBField: string;
    operator?: string;
  }

  export interface ModelAssociationTableParam extends AssociationTableParam {
    selectedAtype: string;
    selectedBtype: string;
    selectedBLabel?: string;
  }

    // 通用枚举类型定义
  export interface OptionsParam {
    value: string | number;
    label: string;
  }

  export interface NodeOptionsParam {
    value: string | number;
    label: string;
    key: string;
  }

  // 节点选择器参数类型定义
  export interface ComputerOptionsParam {
    value: string;
    label: string;
    key: string;
    dataStream?: string;
    nodeType?: string;
    icon?: string;
  }

  export interface ComputerOptionsParamTest {
    title?: string,
    type?: string,
    children?: ComputerOptionsParam[]
  }

  // 分组汇总算子--日期字段参数类型定义
  export interface DateFieldParam extends NodeOptionsParam {
    period: string | number;
    // 时间流程图相关字段
    timeFlowData?: {
      nodes: any[];
      edges: any[];
      connectionPath: string[]; // 连接路径，如 ['year', 'quarter', 'month']
    };
    useFlowChart?: boolean; // 是否使用流程图模式
    // 新的边关系格式
    periodEdges?: Array<{
      start: number;
      end: number;
    }>;
  }

  // 定义全局计算算子的类型定义
  export interface GlobalCalculationParam {
    resource: ResourceParam;
    source: AllInputParam[];
    trans: EditDrawerParam[];
    sink: AllOutputParam[];
  }

  export interface ResourceParam {
    jobName?: string;
    mode?: string;
    remark?: string;
  }

  // 定义所有输入算子的类型定义
  export interface AllInputParam {
    id: string;
    label?: string;
    name?: string;
    nodeType?: string;
    type?: string;
    to?: string;
    fieldsArray?: string;
    dataStream?: string;
    // 定时记录算子
    startTime?: string | number;
    latenessDay?: string | number;
    dataIds?: number[];
    // 业务维度配置算子
    modelLabel?: string;
    tableData?: FieldTableParam[];
    sql?: string;
    fields?: FieldTableParam[];
    // 调试预览
    debugModel?: boolean;
    debugDataSource?: string;
    filterTable?: FieldTableParam[];
    // 模型数据输入算子
    syncModel?: string;   // 模型名称
    // 流程变量
    variables?: VariableParam[];
  }

  // 流程变量参数定义
  export interface VariableParam {
    name: string;
    label: string;
    value: string;
  }

  // join连接算子 -- 弹窗表格数据定义
  export interface FieldTableParam {
    field: string | number;
    description: string;
    fieldType: string;
    frontedType: string
    label?: string;
    formula?: string;
  }

  // 业务维度配置算子 -- 输入源数据定义
  export interface ModelsParam {
    id?: string | number;
    modelLabel: string;
    linkedLabel?: string;
    sql?: string;
    notes?: string;
    fields?: FieldTableParam[];
  }

  // 多个join算子保存合并后字段使用map
  export interface InputModelParam {
    [key: string]: ModelsParam
  }

  export interface AllOutputParam {
    id: string;
    name?: string;
    rediskey?: string[];
    nodeType?: string;
    type?: string;
    redisTableName?: string;
    from?: string[],
    dataStream?: string;
    // 模型输出算子类型
    syncMethod?: string;
    syncModel?: string;
    syncModelLabel?: string;
    modelAssociation?: ModelAssociationTableParam[];
    syncField?: (string | number)[];
    remark?: string;
    repeatProcessMethod?: string;
    propertyList?: PropertyList[];
    // 调试预览
    debugModel?: boolean;
    debugDataSource?: string;
    filterTable?: FieldTableParam[];
  }

  export interface ModelParam {
    reserveFlag: boolean;
    constructionType: string;
    authorizable: boolean;
    modelPackage: string;
    alias: string;
    description: string;
    id: number;
    label: string;
    version: string;
    propertyList?: PropertyList[];
  }

  export interface ModelFieldParam {
    [key: string]: PropertyList[]
  }
  
  export interface PropertyList {
    nullable: boolean;
    defaultValue: string;
    dataType: string;
    rangeMin: any;
    description: any;
    formatConstraint: any;
    modelLabel: string;
    reserveFlag: boolean;
    rangeMax: any;
    propertyLabel: string;
    repeatable: boolean;
    alias: string;
    unitSymbol: any;
    id: number;
    maxLength: any;
  }

  //流程图设计页面--各个算子的最终结果字段对应方法字段
  export interface NodeTypeFieldMapParam {
    [key: string]: function
  }

  // 流程图设计页面--各个算子的方法返回定义
  export interface ConnectDataParam {
    getTableData: FieldTableParam[],
    filterData: AllInputParam | EditDrawerParam
  }

  // 流程设计页面--筛选算子中过滤条件定义

  export interface FilterComponentParam extends NodeOptionsParam {
    componentName: string;
  }

  export interface FilterConditionArrayParam {
    selectField: string | number;
    selectFunctionName: string;
    inputValue?: number | string | (number | string)[] | null;
    rangeType?: string;
  }

  export interface FilterConditionParam {
    [key: string]: FilterComponentParam[]
  }

  export interface FilterValueOptionsParam {
    [key: string]: any
  }

  export interface NodeClearMapParam {
    [key: string]: string[];
  }

  // 表格表头类型定义
  export interface FilterTableParam {
    label: string;
    type?: string;
    prop?: string;
    width?: string;
    minWidth?: string;
    formatter?: any
  }

  // 弹窗内部子组件方法定义
  export interface ChildrenComponentParam {
    getNewData: () => Promise<boolean>;
    checkFieldUpdate?: () => boolean
  }

  // 步骤条类型定义
  export interface StepParam {
    title: string;
    index: number;
    component: any;
  }

  // redis定制业务模型查询字段返回类型定义
  export interface RedisModelParam {
    propertyLabel: string;
    dataType?: string;
    alias?: string;
  }

  // 异常处理算子 -- 异常参数类型定义
  export interface ExceptionParam {
    // 参数字段名
    exceptionField?: string;
    // 参数字段值
    exceptionValue?: number | string | boolean;
  }

  // 异常处理算子 -- 业务场景关联字段
  export interface DynamicFieldParam {
    [key: string]: string | string[]
  }

  // 异常处理算子 -- 业务场景关联字段
  export interface DynamicOptionsParam {
    field: string; // 字段名
    fieldName: string; // 字段中文名
    description: string; // 描述信息
    isChooseMore: boolean; // 该字段是否多选
    isRequired: boolean; // 该字段是否必选
  }
}
