<template>
  <div ref="containerRef">
    <slot></slot>
    <!-- 父级设为body -->
    <Teleport to="body">
      <ul
        v-if="showMenu"
        :style="{ top: y + 'px', left: x + 'px' }"
        class="context-menu"
        @click="handleClick($event)"
      >
        <li v-for="(item, index) in menuList" :key="item.label">
          {{ item.label }}
        </li>
      </ul>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
// 获取菜单的位置和控制是否进行展示
import useContextMenu from "./useContextMenu";
const containerRef = ref();
const { x, y, showMenu } = useContextMenu(containerRef);

const props = withDefaults(
  defineProps<{
    menuList: MenuListParam[];
  }>(),
  {}
);
// 声明一个事件，选中菜单项的时候返回数据
const emit = defineEmits(["select"]);

function handleClick(e: MouseEvent) {
  // 获取点击的菜单项
  const target = e.target as HTMLLIElement;
  if (target.tagName !== "LI") return;
  const item = props.menuList.find((item) => item.label === target.textContent);
  if (!item) return;
  // 选中菜单后关闭菜单
  showMenu.value = false;
  // 并返回选中的菜单
  emit("select", item);
}
</script>

<style scoped>
.context-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 100px;
  background-color: #fff;
  border: 1px solid #ccc;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  z-index: 9999;
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.context-menu li {
  padding: 10px;
  cursor: pointer;
}
.context-menu li:hover {
  background-color: #f5f5f5;
}
</style>
