import { Ref } from "vue";
// 进行菜单的交互监听处理
export default function useContextMenu(
  containerRef: HTMLElement | Ref<HTMLElement>
) {
  const showMenu = ref(false);
  const x = ref(0);
  const y = ref(0);

  // 右键菜单
  const handleContentMenu = (e: MouseEvent) => {
    e.preventDefault(); // 阻止浏览器的默认行为
    e.stopPropagation(); // 阻止冒泡
    x.value = e.clientX;
    y.value = e.clientY;
    showMenu.value = true;
  };

  // 点击空白处关闭菜单
  const closeMenu = () => {
    showMenu.value = false;
  };

  onMounted(() => {
    const div = isRef(containerRef) ? containerRef.value : containerRef;
    // 监听右键鼠标事件
    div.addEventListener("contextmenu", handleContentMenu);
    // 监听点击事件
    window.addEventListener("click", closeMenu, true);
    // 处理 window 的 contextmenu 事件，用来关闭之前打开的菜单
    window.addEventListener("contextmenu", closeMenu, true);
  });

  onBeforeUnmount(() => {
    const div = isRef(containerRef) ? containerRef.value : containerRef;
    // 进行销毁监听器操作
    div.removeEventListener("contextmenu", handleContentMenu);
    window.removeEventListener("click", closeMenu, true);
    window.removeEventListener("contextmenu", closeMenu, true);
  });
  return {
    x,
    y,
    showMenu,
  };
}
