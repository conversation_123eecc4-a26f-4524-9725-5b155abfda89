<template>
  <div class="logger-level-manager">
    <div class="header-controls">
      <el-button type="default" @click="goBack" style="margin-bottom: 16px;">返回主页面</el-button>
      <el-input 
        v-model="taskManagerId" 
        placeholder="输入任务管理器ID" 
        style="width: 250px; margin-left: 16px; margin-bottom: 16px;" 
        @change="loadLoggers"
      >
        <template #prepend>任务管理器</template>
      </el-input>
      <el-button type="success" @click="loadLoggers" :loading="loading" style="margin-left: 16px; margin-bottom: 16px;">刷新列表</el-button>
    </div>
    
    <div class="search-controls">
      <el-input v-model="search" placeholder="搜索 Logger" style="width: 300px; margin-bottom: 16px;" />
      <el-button type="primary" @click="batchSetDialogVisible = true" :disabled="selectedLoggers.length === 0" style="margin-bottom: 16px; margin-left: 16px;">
        批量设置日志级别 ({{ selectedLoggers.length }})
      </el-button>
    </div>
    <el-table
      :data="filteredLoggers"
      style="width: 100%"
      :default-sort="{prop: 'loggerName', order: 'ascending'}"
      @selection-change="handleSelectionChange"
      ref="loggerTableRef"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="loggerName" label="Logger名称" sortable />
      <el-table-column prop="logLevel" label="日志级别">
        <template #default="scope">
          <el-select v-model="scope.row.logLevel" placeholder="选择级别" @change="level => handleLevelChange(scope.row, level)">
            <el-option v-for="level in levels" :key="level" :label="level" :value="level" />
          </el-select>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-model="batchSetDialogVisible" title="批量设置日志级别" width="600px">
      <el-table :data="selectedLoggers" style="width: 100%">
        <el-table-column prop="loggerName" label="Logger名称" />
        <el-table-column label="新日志级别">
          <template #default="scope">
            <el-select v-model="scope.row.newLevel" placeholder="选择级别">
              <el-option v-for="level in levels" :key="level" :label="level" :value="level" />
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="batchSetDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchSet">确定批量设置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { fetchLoggerLevelList, updateLoggerLevel, batchUpdateLoggerLevel } from '@/api/processListApi/loggerLevelApi';

const loggers = ref<Array<{ loggerName: string; logLevel: string }>>([]);
const search = ref('');
const taskManagerId = ref('localhost:43463-412508');
const loading = ref(false);
const levels = ['DEBUG', 'INFO', 'WARN', 'ERROR'];
const loggerTableRef = ref();
const selectedLoggers = ref<Array<{ loggerName: string; logLevel: string; newLevel?: string }>>([]);
const batchSetDialogVisible = ref(false);

const router = useRouter();

const filteredLoggers = computed(() => {
  if (!search.value) return loggers.value;
  return loggers.value.filter(l => l.loggerName.toLowerCase().includes(search.value.toLowerCase()));
});

function handleSelectionChange(selection: Array<{ loggerName: string; logLevel: string }>) {
  selectedLoggers.value = selection.map(row => ({ ...row, newLevel: row.logLevel }));
}

function handleLevelChange(row: { loggerName: string; logLevel: string }, level: string) {
  updateLoggerLevel(row.loggerName, level, taskManagerId.value).then(() => {
    row.logLevel = level;
    ElMessage.success(`已将 ${row.loggerName} 的日志级别更新为 ${level}`);
  }).catch(error => {
    ElMessage.error(`更新日志级别失败: ${error.message}`);
  });
}

function handleBatchSet() {
  const updates = selectedLoggers.value.map(row => ({
    loggerName: row.loggerName,
    logLevel: row.newLevel || row.logLevel
  }));
  
  batchUpdateLoggerLevel(updates, taskManagerId.value).then(() => {
    selectedLoggers.value.forEach(row => {
      const target = loggers.value.find(l => l.loggerName === row.loggerName);
      if (target) target.logLevel = row.newLevel || target.logLevel;
    });
    batchSetDialogVisible.value = false;
    ElMessage.success(`成功批量更新了 ${updates.length} 个Logger的日志级别`);
  }).catch(error => {
    ElMessage.error(`批量更新失败: ${error.message}`);
  });
}

function goBack() {
  router.push({ path: '/' });
}

function loadLoggers() {
  loading.value = true;
  fetchLoggerLevelList(taskManagerId.value).then(response => {
    // 直接处理Flink API返回的数据结构，不需要.data
    if (response && response.loggerLevelInfos && Array.isArray(response.loggerLevelInfos)) {
      loggers.value = response.loggerLevelInfos;
      ElMessage.success(`成功加载了 ${loggers.value.length} 个Logger信息`);
    } else {
      ElMessage.warning('未获取到Logger信息');
      console.warn('意Flink API返回数据格式：', response);
    }
  }).catch(error => {
    ElMessage.error(`获取Logger列表失败: ${error.message}`);
    // 如果请求失败，可以使用模拟数据进行测试
    loggers.value = [
      { loggerName: 'ROOT', logLevel: 'INFO' },
      { loggerName: 'com.alibaba.druid.pool.DruidConnectionHolder', logLevel: 'INFO' },
      { loggerName: 'org.apache.flink.runtime', logLevel: 'INFO' },
    ];
  }).finally(() => {
    loading.value = false;
  });
}

onMounted(() => {
  loadLoggers();
});
</script>

<style scoped>
.logger-level-manager {
  padding: 24px;
}

.header-controls {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.search-controls {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
