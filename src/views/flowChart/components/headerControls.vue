<template>
  <div class="header-controls">
    <div class="back-btn" @click="backClick">
      <el-icon style="vertical-align: middle"><Back /></el-icon>
      <span>返回列表</span>
    </div>
    <div class="header-text">
      <span>{{ alias }}</span>
      <el-tooltip
        content="流程备注"
        placement="right"
        trigger="hover"
        :disabled="visible"
      >
        <!-- 为了使得tooltip和popover同时生效，需要再两者之间添加一个span标签 -->
        <span>
          <el-popover
            :visible="visible"
            :width="300"
            placement="bottom"
            popper-class="popoverTooltip"
          >
            <template #reference>
              <svg-icon
                name="processDescription"
                class="icon-style"
                aria-hidden="false"
                @click="clickVisible"
              ></svg-icon>
            </template>
            <div class="popover-style" v-click-outside="remarkCancel">
              <el-input
                v-model="textarea"
                :rows="6"
                type="textarea"
                placeholder="请输入内容"
              />
              <!-- 使用v-if解决按钮关闭后出现aria-hidden的问题 -->
              <div v-if="visible" class="remark-btn">
                <el-button size="small" @click="remarkCancel">取消</el-button>
                <el-button
                  size="small"
                  type="primary"
                  @click="remarkClick"
                  v-show="isShowButtom"
                >
                  保存
                </el-button>
              </div>
            </div>
          </el-popover>
        </span>
      </el-tooltip>
    </div>
    <el-button
      type="primary"
      class="save-btn"
      @click="saveClick"
      v-show="isShowButtom"
    >
      保存
    </el-button>
    <el-button class="save-btn" @click="runningClick" v-show="isShowButtom">
      运行
    </el-button>
    <el-button class="save-btn" @click="debuggerClick" v-show="isShowButtom">
      调试
    </el-button>
    <el-button
      v-show="false"
      type="primary"
      class="save-btn"
      @click="emit('clearClick')"
    >
      一键清空
    </el-button>
    <el-button
      type="primary"
      class="save-btn"
      @click="debuggerLogClick"
      v-show="false"
    >
      调试日志
    </el-button>
    <div class="save-btn" v-show="showLineType">
      <el-radio-group v-model="radio" @change="radioChange">
        <template v-for="item in PATH_OPTIONS">
          <el-radio-button :label="item.label" :value="item.value" />
        </template>
      </el-radio-group>
    </div>
    <debuggerDrawer v-model="showDebDrawer"></debuggerDrawer>
  </div>
</template>

<script setup lang="ts">
import { Back, ChatLineSquare } from "@element-plus/icons-vue";

import { ClickOutside as vClickOutside } from "element-plus";

import { PATH_OPTIONS, showLineType } from "@/utils/globalField";

import { useRouter } from "vue-router";
const router = useRouter();

const props = defineProps<{
  id: string;
  jobName: string;
  alias: string;
  isTableLayout: boolean;
  status: string;
}>();

const isShowButtom = computed(() => {
  return props.status === "design";
});

// 声明一个事件，选中菜单项的时候返回数据
const emit = defineEmits(["clearClick"]);

function backClick() {
  router.push({
    path: "/",
    state: {
      isTableLayout: props.isTableLayout,
    },
  });
}

// 更新边线的类型
import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

const radio = ref(flowChartStore.edgeType);

function radioChange(val?: string | number | boolean) {
  if (!val) return;
  flowChartStore.updateEdgeType(val as string);
}

import { useVueFlow } from "@vue-flow/core";

const { toObject } = useVueFlow();

import { flowDepthCheck } from "@/utils/flowDepthCheck";

import { maxDepth } from "@/utils/globalField";

// 流程图保存
function saveClick() {
  const callback = () => {
    ElMessage.success("保存成功");
  };
  saveApiFunction(callback);
}

// 调用保存接口
function saveApiFunction(callback?: Function, isRunning?: boolean) {
  const edges = toObject().edges;
  const nodes = toObject().nodes;
  // 判断流程图深度是否超出限制
  if (flowDepthCheck(nodes, edges, maxDepth)) {
    return;
  }
  Promise.all([
    new Promise((resolve) => {
      saveFlow(resolve);
    }),
    new Promise((resolve) => {
      saveArgues(resolve);
    }),
  ]).then(() => {
    callback && callback();
  });
}

// 接口导入
import customApi from "@/api/custom";

// 转换functions数据为后端格式的辅助函数
function convertFunctionsToBackendFormat(functions: string[], nodeId: string) {
  if (!functions || functions.length === 0) {
    return [];
  }

  // 导入常见指标定义
  const COMMON_METRICS = [
    { id: 4, key: "vMin", label: "最小值", dataType: "double" },
    { id: 3, key: "vMax", label: "最大值", dataType: "double" },
    { id: 11, key: "vAvg", label: "平均值", dataType: "double" },
    { id: 10, key: "vSum", label: "累加值", dataType: "double" },
    { id: 6, key: "vCp95", label: "95百分位值", dataType: "double" },
    { id: 17, key: "vCount", label: "计数", dataType: "double" }
  ];

  return functions.map(funcKey => {
    const metric = COMMON_METRICS.find(m => m.key === funcKey);
    if (metric) {
      return {
        func: metric.id,
        funcField: `${funcKey}${nodeId}`
      };
    }
    return null;
  }).filter(Boolean);
}

function saveArgues(callback: Function) {
  // 进行错误兼容问题，判断参数数据和布局节点数据是否可以对上
  const nodes = toObject().nodes;
  const nodesId = nodes.map((item) => item.id);

  const argues = flowChartStore.arguesData;
  const newArgues = argues.map((argue) => {
    return {
      sink: argue.sink.filter((item) => nodesId.includes(item.id)),
      trans: argue.trans.filter((item) => nodesId.includes(item.id)).map((item: any) => {
        // 如果是分组汇总算子且有functions字段，进行转换
        if (item.type === 'StreamGroupByFunction' && item.functions) {
          const convertedItem = { ...item };
          convertedItem.functions = convertFunctionsToBackendFormat(item.functions, item.id);
          return convertedItem;
        }
        return item;
      }),
      source: argue.source.filter((item) => nodesId.includes(item.id)),
      resource: argue.resource,
    };
  });

  const param = {
    id: props.id,
    argues: JSON.stringify(newArgues),
  };
  try {
    customApi.saveFlinkJob(param).then((res) => {
      callback && callback();
    });
  } catch (error) {
    console.log(error);
  }
}

function saveFlow(callback: Function) {
  const param = {
    id: props.id,
    jobName: props.jobName,
    layout: JSON.stringify([toObject()]),
  };
  try {
    customApi.updateLayout(param).then((res) => {
      callback && callback();
    });
  } catch (error) {
    console.log(error);
  }
}

function runningClick() {
  const callback = () => {
    const argues = JSON.stringify(flowChartStore.arguesData);
    saveRuning(argues, true);
  };
  saveApiFunction(callback);
}

async function saveRuning(argues: string, bool: boolean = false) {
  const param = {
    id: props.id,
    jobName: props.jobName,
    argues: argues,
  };
  try {
    const res = await customApi.runFlinkJob(param);
    ElMessage.success(res.msg);
    if (bool) {
      backClick();
    }
  } catch (error) {
    console.log(error);
  }
}

function debuggerClick() {
  const callback = () => {
    const debugData = cloneDeep(flowChartStore.arguesData);
    // 添加调试按钮
    debugData[0].resource.mode = "debug";
    const argues = JSON.stringify(debugData);
    saveRuning(argues);
  };
  saveApiFunction(callback);
}

import debuggerDrawer from "./CustomDrawer/debuggerDrawer.vue";

const showDebDrawer = ref<boolean>(false);

function debuggerLogClick() {
  showDebDrawer.value = true;
}

// 文本旁边的流程备注信息
const textarea = ref<string>("");

const visible = ref(false);

function clickVisible() {
  textarea.value = flowChartStore.flowRemark;
  visible.value = true;
}

function remarkClick() {
  flowChartStore.updateFlowRemark(textarea.value);
  visible.value = false;
}

function remarkCancel() {
  textarea.value = flowChartStore.flowRemark;
  visible.value = false;
}
</script>

<style lang="scss" scoped>
.header-controls {
  width: 100%;
  height: 64px;
  background-color: #fff;
  //border-bottom: 1px solid #ccc;
  box-sizing: border-box;
  padding: 16px 24px;
  line-height: 32px;
  .back-btn {
    float: left;
    cursor: pointer;
    span {
      font-size: 14px;
      margin-left: 8px;
      vertical-align: middle;
    }
  }
  .save-btn {
    float: right;
    margin-left: 8px;
  }
  .header-text {
    float: left;
    margin-left: 40%;
    font-size: 16px;
  }
}
.remark-btn {
  text-align: right;
  margin-top: 8px;
}
.icon-style {
  cursor: pointer;
  vertical-align: middle;
  margin-left: 8px;
  outline: none;
}
</style>
<style lang="scss">
// 自定义popver样式
.popoverTooltip {
  padding: 0px !important;
  .popover-style {
    padding: 12px;
    height: 100%;
  }
}
</style>
