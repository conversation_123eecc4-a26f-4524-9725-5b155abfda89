<template>
  <!-- 右键菜单栏 -->
  <ContextMenu
    id="node_class"
    :menuList="menuList"
    @select="selectItem"
    @dblclick.prevent="editOneNode"
    @keydown="keydownClick"
    tabindex="0"
  >
    <div class="top_label">
      <span>{{ topLabel }}</span>
      <span>{{ id }}</span>
    </div>
    <div class="top_text">
      {{ data.name }}
    </div>
    <!-- 边框连接点 -->
    <Handle
      type="target"
      :position="Position.Left"
      v-if="data.nodeType === 'sink'"
      :is-valid-connection="isValidConnection"
    />
    <Handle
      type="source"
      :position="Position.Right"
      v-if="data.nodeType === 'source'"
      :is-valid-connection="isValidConnection"
    />
    <Handle
      type="target"
      :position="Position.Left"
      v-if="!whiteTypeList.includes(data.nodeType)"
      :is-valid-connection="isValidConnection"
    />
    <Handle
      type="source"
      :position="Position.Right"
      v-if="!whiteTypeList.includes(data.nodeType)"
      :is-valid-connection="isValidConnection"
    />
  </ContextMenu>
</template>

<script setup lang="ts">
import {
  Handle,
  Position,
  useVueFlow,
  type ValidConnectionFunc,
} from "@vue-flow/core";

const { removeNodes, toObject } = useVueFlow();

import { sourceLimit } from "@/utils/globalField";

// 进行各个算子的连接线数量校验处理
const isValidConnection: ValidConnectionFunc = (connection, elements) => {
  const { edges, targetNode, sourceNode } = elements;
  const { id, data } = targetNode;
  const sourceData = sourceNode.data;
  // 输入节点不能作为target结尾点
  if (data.nodeType === "source") return false;
  // 输出节点不能作为source起始点
  if (sourceData.nodeType === "sink") return false;
  // 按照陈练练要求，业务维度算子只能连接join连接算子
  if (sourceData.type === "CfgSource" && data.type !== "StreamJoinFunction") {
    return false;
  }
  // 获取当前所有算子的连接线数量并进行判断
  const length = sourceLimit[data.type] || 1;
  const curLength = edges.filter((item) => item.target === id).length;
  return length > curLength;
};

const props = defineProps<{
  id: string;
  data: Data;
}>();

// 声明一个事件，选中菜单项的时候返回数据
const emit = defineEmits(["editNodeInfo"]);

// 引入右键菜单栏
import ContextMenu from "@/components/ContextMenu/ContextMenu.vue";

const menuList = ref([
  {
    label: "编辑",
  },
  {
    label: "删除",
  },
]);

function selectItem(item: MenuListParam) {
  if (item.label === "编辑") {
    editOneNode();
  } else if (item.label === "删除") {
    removeOneNode();
  }
}

// 监听键盘事件delete，删除节点
function keydownClick(e: KeyboardEvent) {
  if (e.key === "Delete") {
    removeOneNode();
  }
}

import { clearChildrenNode } from "@/utils/common";

function removeOneNode() {
  // 获取当前节点下的所有子节点
  clearChildrenNode(props.id, toObject().nodes, toObject().edges, "deleteNode");
  removeNodes(props.id);
}

function editOneNode() {
  const output = {
    id: props.id,
    name: props.data.name,
    nodeType: props.data.nodeType,
    type: props.data.type,
    dataStream: props.data.dataStream,
  };
  // 并返回选中的菜单
  emit("editNodeInfo", output);
}

import { nodeTypeLabelMapTest } from "@/utils/globalField";
// 固定节点类型名称
const topLabel = computed(() => {
  const nodeLabel = nodeTypeLabelMapTest
    .flatMap((item) => item.children)
    .filter((child) => child?.value === props.data.type);
  return nodeLabel.length > 0 ? nodeLabel[0]?.label : "节点";
});

// 定义节点类型列表白名单
const whiteTypeList = ["source", "sink"];
</script>

<style lang="scss" scoped>
.top_label {
  height: 16px;
  line-height: 16px;
  padding: 0 4px;
  background: var(--el-color-primary);
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  span {
    font-size: 8px;
  }
}
.top_text {
  padding: 10px 0 15px 0;
  min-height: 16px;
}
</style>
