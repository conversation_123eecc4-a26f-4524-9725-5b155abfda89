<template>
  <div class="page">
    <t-steps :current="stepActive" readonly style="margin-bottom: 24px">
      <template v-for="(item, index) in stepData" :key="index">
        <t-step-item :title="item.title" />
      </template>
    </t-steps>
    <template v-for="(item, index) in stepData" :key="index">
      <component
        :is="item.component"
        v-show="stepActive === item.index"
        ref="childrenStepRef"
        :showDrawer="showDrawer"
        :stepActive="stepActive"
        :keyMenuItem="keyMenuItem"
        :componentPageName="componentPageName"
      ></component>
    </template>
  </div>
</template>

<script setup lang="ts">
// 引入TDesign的组件
import { Steps as TSteps, StepItem as TStepItem } from "tdesign-vue-next";
// 获取父组件的ref
const props = defineProps<{
  stepData: StepParam[];
  showDrawer?: boolean;
  keyMenuItem?: string;
  componentPageName: string;
}>();

const stepLength = computed(() => {
  return props.stepData.length - 1;
});

// 步骤条的当前激活的步骤 --- 步骤条专用
interface StepModel {
  previousTime: number; // 上一步的时间
  nextTime: number; // 下一步的时间
}

// 监听祖先元素的步骤条是否变化
const stepModel = inject("stepModel") as Ref<StepModel>;

watch(
  () => [stepModel.value.previousTime, stepModel.value.nextTime],
  ([newPreviousTime, newNextTime], [oldPreviousTime, oldNextTime]) => {
    // 检查 previousTime 是否发生变化
    if (newPreviousTime !== oldPreviousTime) {
      previousStep();
    }

    // 检查 nextTime 是否发生变化
    if (newNextTime !== oldNextTime) {
      nextStep();
    }
  }
);

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 数据监听  -- 通过监听去对步骤进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    stepActive.value = 0;
    flowChartStore.updatePageSteps(stepActive.value);
  },
  {
    immediate: true,
  }
);

const stepActive = ref(0);

const childrenStepRef = ref<ChildrenComponentParam[]>([]);

// 引入通用的校验规则
import { getFormRulesAll, checkAllFieldUpdate } from "@/utils/common";

// 判断页面中多个步骤条的校验是否通过
async function getNewData() {
  const ruleBool = await getFormRulesAll(childrenStepRef);
  return ruleBool;
}

function previousStep() {
  if (stepActive.value > 0) {
    stepActive.value--;
    flowChartStore.updatePageSteps(stepActive.value);
  }
}

async function nextStep() {
  // 先判断当前页面是否验证通过
  const childRuleBool1 = await childrenStepRef.value[
    stepActive.value
  ].getNewData();
  if (!childRuleBool1) return;
  if (stepActive.value < stepLength.value) {
    stepActive.value++;
    flowChartStore.updatePageSteps(stepActive.value);
  }
}

async function checkFieldUpdate() {
  const ruleBool = await checkAllFieldUpdate(childrenStepRef);
  return ruleBool;
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
  checkFieldUpdate,
});
</script>

<style scoped></style>
