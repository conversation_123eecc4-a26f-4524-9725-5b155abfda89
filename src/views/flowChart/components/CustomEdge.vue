<template>
  <g class="custom-g-edge">
    <!-- 圆形markStart -->
    <!-- <circle :cx="sourceX" :cy="sourceY" r="4" fill="#29a3ff" /> -->
    <!-- path -->
    <path :d="path[0]" stroke-width="2.5" />
    <!-- 用于可交互区域路径（宽度更大且隐藏不展示） -->
    <path
      :d="path[0]"
      style="
        position: absolute;
        opacity: 0;
        left: -4px;
        top: -4px;
        width: calc(100% + 8px);
        height: calc(100% + 8px);
      "
    />
    <!-- 箭头markEnd -->
    <path
      :transform="transform"
      :d="`M ${targetX} ${targetY + 2} L ${targetX - 5} ${targetY - 10} L ${
        targetX + 5
      } ${targetY - 10} Z`"
      class="custom-g-edge-arrow"
    />
  </g>
</template>

<script setup lang="ts">
import {
  getSmoothStepPath,
  getStraightPath,
  getSimpleBezierPath,
  EdgeProps,
} from "@vue-flow/core";

const props = defineProps<EdgeProps>();

const path = computed(() => {
  switch (props.type) {
    case "straight":
      return getStraightPath(props);
    case "default":
      return getSimpleBezierPath(props);
    default:
      return getSmoothStepPath(props);
  }
});

const transform = computed(() => {
  return getArrowTransform();
});

function getArrowTransform() {
  switch (props.type) {
    case "straight":
      const angle = getAngle();
      return `rotate(${angle} ${props.targetX} ${props.targetY})`;
    default:
      return getArrowRotate();
  }
}

function getArrowRotate() {
  const { targetPosition } = props;
  if (targetPosition === "top") {
    return `rotate(0 ${props.targetX} ${props.targetY})`;
  }
  if (targetPosition === "bottom") {
    return `rotate(180 ${props.targetX} ${props.targetY})`;
  }
  if (targetPosition === "left") {
    return `rotate(-90 ${props.targetX} ${props.targetY})`;
  }
  if (targetPosition === "right") {
    return `rotate(90 ${props.targetX} ${props.targetY})`;
  }
}

// 角度计算方法
function getAngle() {
  const { sourceX, sourceY, targetX, targetY } = props;
  let angle =
    Math.atan2(targetY - sourceY, targetX - sourceX) * (180 / Math.PI);
  return angle - 90;
}

import { useVueFlow } from "@vue-flow/core";

const { removeEdges } = useVueFlow();

const handleKeyDown = (e: KeyboardEvent) => {
  // 监听键盘事件delete，删除节点
  if (e.key === "Delete" && props.selected) {
    removeEdges(props.id);
  }
};

// 在组件挂载后添加事件监听器
onMounted(() => {
  window.addEventListener("keydown", handleKeyDown);
});

// 在组件卸载前移除事件监听器
onBeforeUnmount(() => {
  window.removeEventListener("keydown", handleKeyDown);
});
</script>

<style lang="scss" scoped>
.custom-g-edge {
  fill: none;
  stroke: var(--flow-edge-default-color);
  stroke-width: 30;
  .custom-g-edge-arrow {
    fill: var(--flow-edge-default-color);
    stroke: none;
  }
}

.custom-g-edge:hover {
  stroke: var(--flow-edge-hover-color);
  .custom-g-edge-arrow {
    fill: var(--flow-edge-hover-color);
  }
}
</style>
