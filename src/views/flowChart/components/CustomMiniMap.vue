<template>
  <el-popover
    placement="top-start"
    width="200"
    popper-style="height: 160px"
    :visible="visible"
  >
    <template #reference>
      <el-icon size="24" class="icon-style" @click="visible = !visible">
        <MapLocation />
      </el-icon>
    </template>
    <MiniMap pannable />
  </el-popover>
</template>

<script setup lang="ts">
import { MapLocation } from "@element-plus/icons-vue";
import { MiniMap } from "@vue-flow/minimap";

const visible = ref(false);
</script>

<style lang="scss" scoped>
.icon-style {
  height: 32px;
  width: 32px;
  position: absolute;
  right: 42px;
  bottom: 24px;
  background: #fefefe;
  z-index: 4;
}
.vue-flow__panel {
  margin: 0;
  :deep(.vue-flow__minimap-node.selected) {
    fill: #165dff;
  }
}
</style>
