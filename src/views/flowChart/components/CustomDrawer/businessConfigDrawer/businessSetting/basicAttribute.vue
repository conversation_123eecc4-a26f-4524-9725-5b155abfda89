<template>
  <div class="page">
    <el-form
      class="form-style"
      ref="ruleFormRef"
      label-width="auto"
      label-position="top"
      require-asterisk-position="right"
      :model="inputOperatorData"
      :rules="inputOperatorDataRules"
      :show-message="false"
    >
      <el-form-item label="节点名称" prop="name">
        <el-input v-model="inputOperatorData.name" maxlength="20"></el-input>
        <div class="form-item-tips">指定任何有意义且能描述本节点的说明</div>
      </el-form-item>
      <el-form-item label="节点ID" prop="id">
        <el-input v-model="inputOperatorData.id" disabled></el-input>
        <div class="form-item-tips">节点ID不能重复</div>
      </el-form-item>
      <el-form-item label="输入源" prop="modelLabel">
        <el-select
          v-model="inputOperatorData.modelLabel"
          filterable
          @change="changeSource"
        >
          <el-option
            v-for="item in inputSourceOptions"
            :key="item.id"
            :label="item.notes"
            :value="item.modelLabel"
          />
        </el-select>
        <div class="form-item-tips">请选择一个数据源</div>
      </el-form-item>
    </el-form>
    <!-- 新增流程变量输入表格 -->
    <el-table :data="variables" style="width: 100%; margin-bottom: 16px;">
      <el-table-column label="变量名称" prop="name">
        <template #default="scope">
          <el-input v-model="scope.row.name" placeholder="请输入变量名称" />
        </template>
      </el-table-column>
      <el-table-column label="变量标识" prop="label">
        <template #default="scope">
          <el-input v-model="scope.row.label" placeholder="请输入变量标识" />
        </template>
      </el-table-column>
      <el-table-column label="值" prop="value">
        <template #default="scope">
          <el-input v-model="scope.row.value" placeholder="请输入值" />
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="danger" size="small" @click="removeVariable(scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-button type="primary" @click="addVariable">新增变量</el-button>
  </div>
</template>

<script setup lang="ts">
// 获取父组件的ref
const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
  componentPageName: string;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<AllInputParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    inputOperatorData.value = cloneDeep(inputData.value);
    inputOperatorData.value.to = inputData.value.id;
    // 初始化 variables 数据
    variables.value = cloneDeep(inputData.value.variables || []);
    queryModelsData();
    // 延迟进行表单校验的清空操作才会生效,处理下拉框的change导致进入弹窗的校验触发
    setTimeout(() => {
      clearValidate();
    }, 10);
  },
  {
    immediate: true,
  }
);

const inputOperatorData = ref<AllInputParam>({
  id: "",
  name: "",
});

// 用于监听菜单是否进行切换到指定页面
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (oldVal === props.componentPageName) {
      // 合并 variables 数据到 inputOperatorData
      const dataWithVariables = {
        ...inputOperatorData.value,
        variables: variables.value.filter(v => v.name || v.label || v.value)
      };
      flowChartStore.updateTempData(
        inputData.value.nodeType,
        dataWithVariables
      );
    }
  }
);

import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const inputOperatorDataRules = {
  id: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  modelLabel: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
};

// 输入源
const inputSourceOptions = ref<ModelsParam[]>([]);

// 接口导入
import customApi from "@/api/custom";

// 获取输入源数据
async function queryModelsData() {
  // 清空数据
  inputSourceOptions.value.length = 0;
  // 查询字段表数据
  const res = await customApi.queryModelsData();
  if (res.code === 0) {
    inputSourceOptions.value = cloneDeep(res.data);
  }
  // 判断是否要做回显
  if (inputData.value.modelLabel) {
    changeSource(inputData.value.modelLabel);
  }
}

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

function changeSource(val: string) {
  const target = inputSourceOptions.value.find((item) => {
    return item.id && item.modelLabel === val;
  });
  if (!target) return;
  flowChartStore.updateInputSource(inputData.value.id, target);
}

// 引入通用的校验规则
import { getFormRules } from "@/utils/common";

async function getNewData() {
  // 合并 variables 数据到 inputOperatorData
  const dataWithVariables = {
    ...inputOperatorData.value,
    variables: variables.value.filter(v => v.name || v.label || v.value)
  };
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    dataWithVariables,
    "基本配置"
  );
  return childRuleBool;
}

// 判断输入源是否修改导致字段进行修改
function checkFieldUpdate() {
  if (!inputData.value.modelLabel) return false;
  return inputOperatorData.value.modelLabel !== inputData.value.modelLabel;
}

// 关闭弹窗清楚校验
function clearValidate() {
  ruleFormRef.value?.clearValidate();
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
  checkFieldUpdate,
});

// 新增流程变量输入表格相关逻辑
const variables = ref<VariableParam[]>([]);

function addVariable() {
  variables.value.push({ name: "", label: "", value: "" });
}

function removeVariable(index: number) {
  variables.value.splice(index, 1);
}
</script>

<style lang="scss" scoped>
.page {
  height: calc(100% - 80px);
}
</style>
