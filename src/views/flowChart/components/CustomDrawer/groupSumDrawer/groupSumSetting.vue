<template>
  <div class="page">
    <el-form
      class="form-style"
      ref="ruleFormRef"
      label-position="top"
      require-asterisk-position="right"
      label-width="auto"
      :model="groupSumData"
      :rules="groupSumDataRules"
      :show-message="false"
    >
      <el-form-item label="节点名称" prop="name">
        <el-input v-model="groupSumData.name" maxlength="20"></el-input>
        <div class="form-item-tips">指定任何有意义且能描述本节点的说明</div>
      </el-form-item>
      <el-form-item label="节点ID" prop="id">
        <el-input v-model="groupSumData.id" disabled></el-input>
        <div class="form-item-tips">节点ID不能重复</div>
      </el-form-item>
      <el-form-item label="分组类型" prop="groupType">
        <el-select v-model="groupSumData.groupType" @change="handleGroupTypeChange">
          <el-option
            v-for="item in groupTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div class="form-item-tips">选择分组计算类型</div>
      </el-form-item>

      <!-- 时间分组聚合模式 groupAggregation-->
      <template v-if="groupSumData.groupType === 'groupAggregation'">
        <el-form-item label="分组字段" prop="groupBy">
          <div style="display: flex; width: 100%">
            <el-select
              v-model="groupSumData.groupBy"
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="2"
              @change="selectChange"
            >
              <el-option
                v-for="item in groupByOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="isOptionsDisabled(item)"
              />
            </el-select>
            <el-button
              style="margin-left: 4px"
              :icon="RefreshRight"
              @click="loadField"
            >
              载入字段
            </el-button>
          </div>
          <div class="form-item-tips">
            指定要进行分组的字段，输入字段id后回车（必须要有一个时间格式的字段）
          </div>
        </el-form-item>

        <el-form-item label="计算字段" prop="funcField">
          <div style="display: flex; width: 100%">
            <el-select v-model="groupSumData.funcField" filterable>
              <el-option
                v-for="item in groupByOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button
              style="margin-left: 4px"
              :icon="RefreshRight"
              @click="loadField"
            >
              载入字段
            </el-button>
          </div>
        </el-form-item>
      </template>

      <!-- 周期迭代计算模式 periodIteration-->
      <template v-if="groupSumData.groupType === 'periodIteration'">
        <el-form-item label="分组字段" prop="groupBy">
          <div style="display: flex; width: 100%">
            <el-select
              v-model="groupSumData.groupBy"
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="2"
              @change="selectChange"
            >
              <el-option
                v-for="item in groupByOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="isOptionsDisabled(item)"
              />
            </el-select>
            <el-button
              style="margin-left: 4px"
              :icon="RefreshRight"
              @click="loadField"
            >
              载入字段
            </el-button>
          </div>
          <div class="form-item-tips">
            指定要进行分组的字段，输入字段id后回车（必须要有一个时间格式的字段）
          </div>
        </el-form-item>

        <el-form-item label="选择周期（时间可拖动）">
          <TimeFlowChart 
            ref="timeFlowChartRef"
            :periodEdges="groupSumData.periodEdges"
            @flowChange="handleTimeFlowChange"
          />
        </el-form-item>

        <el-form-item label="计算字段">
          <div style="display: flex; width: 100%">
            <el-select v-model="groupSumData.funcField" filterable>
              <el-option
                v-for="item in groupByOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button
              style="margin-left: 4px"
              :icon="RefreshRight"
              @click="loadField"
            >
              载入字段
            </el-button>
          </div>
        </el-form-item>
      </template>
      
      <!-- 常见指标和自定义指标配置 -->
      <template v-if="groupSumData.groupType">
        <el-form-item label="指标配置">
          <CustomMetricsManager 
            v-model="metricsData"
            @change="handleMetricsChange"
          />
        </el-form-item>
      </template>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="groupSumData.remark"
          type="textarea"
          :rows="1"
        ></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { RefreshRight } from "@element-plus/icons-vue";

// 引入通用的校验规则
import {
  getFormRules,
  getConnectData,
  getNodeFrom,
  getResetField,
  getCommonEnumField,
} from "@/utils/common";

// 引入新组件和常量
import TimeFlowChart from "./timeRecordDrawer/TimeFlowChart/index.vue";
import CustomMetricsManager from "./CustomMetricsManager.vue";
import { GROUP_TYPE_OPTIONS } from "@/utils/globalField";
import { ElMessage } from "element-plus";

// 获取父组件的ref
const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<EditDrawerParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    groupSumData.value = cloneDeep(inputData.value);
    groupSumData.value.from ??= [];
    initData();
    refreshOptions();
    // 延迟进行表单校验的清空操作才会生效,处理下拉框的change导致进入弹窗的校验触发
    setTimeout(() => {
      clearValidate();
    }, 10);
  },
  {
    immediate: true,
  }
);

function initData() {
  groupSumData.value.to = groupSumData.value.id;
  groupSumData.value.groupBy ??= [];
  groupSumData.value.groupType ??= "periodIteration"; // 默认为周期迭代计算

  // 处理向后兼容性：如果存在 commonMetrics 字段，将其迁移到 functions
  if ((groupSumData.value as any).commonMetrics && !groupSumData.value.functions) {
    groupSumData.value.functions = (groupSumData.value as any).commonMetrics;
    delete (groupSumData.value as any).commonMetrics;
  }

  groupSumData.value.functions ??= [];
  groupSumData.value.formulas ??= [];
  if (isEmpty(groupSumData.value.groupBy)) {
    dateFieldData.value.length = 0;
  }
  groupSumData.value.periodField ??= [];
}

// 定义页面名称
const componentPageName = "groupSumSetting";

// 用于监听菜单是否进行切换到指定页面
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (oldVal === componentPageName) {
      flowChartStore.updateTempData(
        inputData.value.nodeType,
        groupSumData.value
      );
    }
  }
);

// 初始默认值
const groupSumData = ref<EditDrawerParam>({
  id: "",
  name: "",
  groupBy: [],
  periodField: [],
  groupType: "periodIteration",
  functions: [],
  formulas: [],
});

// 分组类型选项
const groupTypeOptions = ref(GROUP_TYPE_OPTIONS);

// 时间流程图组件引用
const timeFlowChartRef = ref();

// 指标数据
const metricsData = ref({
  functions: [],
  formulas: []
});

import type { FormInstance, FormRules } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const groupSumDataRules = reactive<FormRules<EditDrawerParam>>({
  id: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  groupType: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  groupBy: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
});

// 处理分组类型变化
function handleGroupTypeChange(value: string) {
  // 清空相关字段
  if (value === 'periodIteration') {
    // 切换到周期迭代计算模式，保留分组字段和计算字段，清空时间分组聚合特有字段
    groupSumData.value.periodField = [];
    dateFieldData.value = [];
  } else if (value === 'groupAggregation') {
    // 切换到时间分组聚合模式，清空周期迭代计算相关字段
    groupSumData.value.periodEdges = [];
  }
}

// 处理时间流程图变化
function handleTimeFlowChange(data: { periodEdges: Array<{ start: number; end: number }> }) {
  groupSumData.value.periodEdges = data.periodEdges;
}

// 处理指标配置变化
function handleMetricsChange(data: { functions: string[]; formulas: any[] }) {
  groupSumData.value.functions = data.functions;
  groupSumData.value.formulas = data.formulas;
}

// 数据所在节点
const locatedNodeOptions = ref<ComputerOptionsParam[]>([]);

import { useVueFlow } from "@vue-flow/core";

const { getIncomers } = useVueFlow();

// 分组字段
const groupByOptions = ref<NodeOptionsParam[]>([]);

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 回显判断是否重新获取字段信息
async function refreshOptions() {
  getlocatedData();
  if (!isEmpty(groupSumData.value.groupBy)) {
    await loadField();
    dateFieldData.value = cloneDeep(groupSumData.value.periodField) || [];
  } else {
    groupByOptions.value.length = 0;
  }

  // 回显指标数据，处理向后兼容性
  let functionsData = groupSumData.value.functions || [];

  // 如果存在旧的 commonMetrics 字段，使用它
  if ((groupSumData.value as any).commonMetrics && functionsData.length === 0) {
    functionsData = (groupSumData.value as any).commonMetrics;
  }

  metricsData.value = {
    functions: functionsData,
    formulas: groupSumData.value.formulas || []
  };
}

// 获取与之连接数据连接
function getlocatedData() {
  locatedNodeOptions.value.length = 0;
  // 获取当前节点的所有入边
  const incomers = getIncomers(inputData.value.id);
  locatedNodeOptions.value = getResetField(incomers);
}

async function loadField() {
  groupByOptions.value.length = 0;
  // 调用方法获取字段数据
  const resData = (await getConnectData(
    locatedNodeOptions.value
  )) as ConnectDataParam;
  const { getTableData, filterData } = resData;
  groupSumData.value.dataStream = filterData?.dataStream;
  if (isEmpty(getTableData)) return;
  groupSumData.value.joinTable = getTableData;
  groupByOptions.value = getCommonEnumField(getTableData);
}

function selectChange(value: string[]) {
  dateFieldData.value.length = 0;
  if (value.length == 0) return;
  const oldPeriod = groupSumData.value.periodField;
  value.forEach((item: string) => {
    const option = groupByOptions.value.find(
      (option) => option.value === item && option.key === "date"
    );
    if (option && option.value) {
      const curPeriod = oldPeriod?.find((key) => key.value === option.value);
      dateFieldData.value.push({
        value: option.value,
        label: option.value + "分组",
        key: option.key,
        period: curPeriod?.period || "",
      });
    }
  });
  groupSumData.value.periodField = cloneDeep(dateFieldData.value);
}

const dateFieldData = ref<DateFieldParam[]>([]);

// 进行限制处理，只允许选择一个日期字段
function isOptionsDisabled(data: NodeOptionsParam) {
  if (dateFieldData.value.length == 0) return false;
  return dateFieldData.value[0].value !== data.value && data.key == "date";
}

// 遍历多个日期字段，进行校验
function dateFieldChange() {
  dateFieldData.value.forEach((item, index) => {
    groupSumDataRules[`periodField.${index}.period`] = [
      {
        required: true,
        trigger: ["blur", "change"],
      },
    ];
  });
}

// 判断是否存在日期字段
const isExistDateField = computed(() => {
  dateFieldChange();
  return dateFieldData.value.length > 0;
});

// 周期选择字段
const periodFieldOptions: OptionsParam[] = [
  {
    value: 17,
    label: "年",
  },
  {
    value: 15,
    label: "季",
  },
  {
    value: 14,
    label: "月",
  },
  {
    value: 13,
    label: "周",
  },
  {
    value: 12,
    label: "日",
  },
  {
    value: 7,
    label: "小时",
  },
];

async function getNewData() {
  // 根据分组类型进行不同的验证
  if (groupSumData.value.groupType === 'groupAggregation') {
    if (groupSumData.value.periodField?.length == 0) {
      ElMessage.error("分组字段中必须包含时间格式字段");
      return false;
    }
  } else if (groupSumData.value.groupType === 'periodIteration') {
    // 周期迭代计算模式的验证
    // if (groupSumData.value.periodField?.length == 0) {
    //   ElMessage.error("分组字段中必须包含时间格式字段");
    //   return false;
    // }
    if (!groupSumData.value.periodEdges || groupSumData.value.periodEdges.length === 0) {
      ElMessage.error("周期迭代计算模式需要配置时间周期连接");
      return false;
    }
  }

  groupSumData.value.from = getNodeFrom(locatedNodeOptions.value);
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    groupSumData.value,
    "基本配置"
  );
  return childRuleBool;
}

// 判断输入源是否修改导致字段进行修改
function checkFieldUpdate() {
  // 检查分组类型是否变化
  if (groupSumData.value.groupType !== inputData.value.groupType) {
    return true;
  }

  // 检查分组字段是否变化（两种模式都有分组字段）
  if (isEmpty(inputData.value.groupBy)) return false;
  if (!isEqual(groupSumData.value.groupBy, inputData.value.groupBy)) {
    return true;
  }

  // 周期迭代计算模式的检查
  if (groupSumData.value.groupType === 'periodIteration') {
    return !isEqual(groupSumData.value.periodEdges, inputData.value.periodEdges);
  }

  return false;
}

// 关闭弹窗清楚校验
function clearValidate() {
  ruleFormRef.value?.clearValidate();
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
  checkFieldUpdate,
});
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
}
</style>
