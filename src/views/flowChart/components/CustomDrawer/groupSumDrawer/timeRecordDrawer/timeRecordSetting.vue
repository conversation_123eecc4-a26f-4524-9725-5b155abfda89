<template>
  <div class="page page-table">
    <el-form
      class="form-style form-table-style"
      ref="ruleFormRef"
      label-position="top"
      require-asterisk-position="right"
      label-width="auto"
      :model="inputOperatorData"
      :rules="inputOperatorDataRules"
      :show-message="false"
    >
      <el-form-item label="节点名称" prop="name">
        <el-input v-model="inputOperatorData.name" maxlength="20"></el-input>
        <div class="form-item-tips">指定任何有意义且能描述本节点的说明</div>
      </el-form-item>
      <el-form-item label="节点ID" prop="id">
        <el-input v-model="inputOperatorData.id" disabled></el-input>
        <div class="form-item-tips">节点ID不能重复</div>
      </el-form-item>
      <el-form-item label="起始时间" prop="startTime">
        <el-date-picker
          v-model="inputOperatorData.startTime"
          type="date"
          placeholder="选择起始时间"
          value-format="x"
          style="width: 100%"
        />
        <div class="form-item-tips" style="width: 100%">选择计算的起始时间</div>
      </el-form-item>
      <el-form-item label="数据ID" prop="dataIds">
        <el-select
          v-model="inputOperatorData.dataIds"
          multiple
          filterable
          allow-create
          default-first-option
          :reserve-keyword="false"
          placeholder="请输入数字ID，支持多个值"
          style="width: 100%"
          @change="handleDataIdsChange"
        >
        </el-select>
        <div class="form-item-tips">输入数字类型的数据ID，支持多个值，非必填</div>
      </el-form-item>
      <el-form-item label="最大延迟时间" prop="latenessDay">
        <el-radio-group v-model="inputOperatorData.latenessDay">
          <template v-for="item in DelayTimeOptions">
            <el-radio :value="item.value">{{ item.label }}</el-radio>
          </template>
        </el-radio-group>
        <div class="form-item-tips">选择计算的最大延迟时间</div>
      </el-form-item>
      <el-form-item prop="tableData" class="form-table-height">
        <el-table :data="tableData" scrollbar-always-on class="fullheight">
          <template v-for="item in fieldTableHeader">
            <el-table-column
              v-bind="item"
              show-overflow-tooltip
            ></el-table-column>
          </template>
        </el-table>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
// 引入通用的校验规则
import { getFormRules, nodeTypeFieldMap } from "@/utils/common";

const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<AllInputParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    inputOperatorData.value = cloneDeep(inputData.value);
    inputOperatorData.value.to = inputData.value.id;
    queryFieldTableData(inputData.value.type);
    // 延迟进行表单校验的清空操作才会生效,处理下拉框的change导致进入弹窗的校验触发
    setTimeout(() => {
      clearValidate();
    }, 10);
  },
  {
    immediate: true,
  }
);

const inputOperatorData = ref<AllInputParam>({
  id: "",
  name: "",
  dataIds: [],
});

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 定义页面名称
const componentPageName = "timeRecordSetting";

// 用于监听菜单是否进行切换到指定页面
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (oldVal === componentPageName) {
      flowChartStore.updateTempData(
        inputData.value.nodeType,
        inputOperatorData.value
      );
    }
  }
);

// 表单校验规则
import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const inputOperatorDataRules = {
  id: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  latenessDay: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
};

import { fieldTableHeader } from "@/utils/globalField";

const tableData = ref<FieldTableParam[]>([]);

// 获取表格字段数据
async function queryFieldTableData(type?: string) {
  if (!type) return;
  // 查询字段表数据
  const getTableData = await nodeTypeFieldMap(type, inputData.value);
  tableData.value = cloneDeep(getTableData);
}

// 处理 dataIds 输入变化，确保只接受数字
function handleDataIdsChange(values: (string | number)[]) {
  const numericValues: number[] = [];
  values.forEach(value => {
    const numValue = Number(value);
    if (!isNaN(numValue) && isFinite(numValue)) {
      numericValues.push(numValue);
    }
  });
  inputOperatorData.value.dataIds = numericValues;
}

// 延迟时间选项--按照陈练练要求添加该字段并写死
const DelayTimeOptions: OptionsParam[] = [
  {
    label: "1天",
    value: 1,
  },
  {
    label: "2天",
    value: 2,
  },
  {
    label: "3天",
    value: 3,
  },
  {
    label: "4天",
    value: 4,
  },
  {
    label: "5天",
    value: 5,
  },
  {
    label: "6天",
    value: 6,
  },
  {
    label: "7天",
    value: 7,
  },
];

// 进行保存校验
async function getNewData() {
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    inputOperatorData.value,
    "基本配置"
  );
  return childRuleBool;
}

// 关闭弹窗清楚校验
function clearValidate() {
  ruleFormRef.value?.clearValidate();
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
});
</script>

<style lang="scss" scoped>
.page {
  .form-table-height {
    height: calc(100% - 420px);
  }
}
</style>
