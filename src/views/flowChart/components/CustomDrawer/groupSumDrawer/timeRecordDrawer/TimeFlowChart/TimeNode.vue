<template>
  <div 
    class="time-node" 
    :class="[`time-node--${data.timeType}`, { 'time-node--selected': selected }]"
    @click="handleClick"
  >
    <div class="time-node__header">
      <div class="time-node__icon">
        <el-icon>
          <Clock v-if="data.timeType === 'hour'" />
          <Calendar v-else-if="data.timeType === 'day'" />
          <Grid v-else-if="data.timeType === 'week'" />
          <Calendar v-else-if="data.timeType === 'month'" />
          <Histogram v-else-if="data.timeType === 'quarter'" />
          <TrendCharts v-else-if="data.timeType === 'year'" />
        </el-icon>
      </div>
      <div class="time-node__title">
        <span v-if="!editing" @dblclick="startEdit">{{ data.label }}</span>
        <el-input
          v-else
          v-model="editValue"
          size="small"
          @blur="finishEdit"
          @keyup.enter="finishEdit"
          ref="editInput"
        />
      </div>
    </div>
    
    <div class="time-node__content">
      <div class="time-node__type">{{ getTimeTypeLabel(data.timeType) }}</div>
      <div class="time-node__value" v-if="data.value">
        {{ data.value }}
      </div>
    </div>

    <!-- 连接点 -->
    <Handle
      type="target"
      position="left"
      class="time-node__handle time-node__handle--target"
    />
    <Handle
      type="source"
      position="right"
      class="time-node__handle time-node__handle--source"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { Handle } from '@vue-flow/core'
import {
  Clock,
  Calendar,
  Grid,
  Histogram,
  TrendCharts
} from '@element-plus/icons-vue'

interface TimeNodeData {
  timeType: 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year'
  label: string
  value?: string | number
}

interface Props {
  data: TimeNodeData
  selected?: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  updateLabel: [label: string]
  click: []
}>()

const editing = ref(false)
const editValue = ref('')
const editInput = ref()

const getTimeTypeLabel = (type: string) => {
  const labels = {
    hour: '小时',
    day: '天',
    week: '周',
    month: '月',
    quarter: '季',
    year: '年'
  }
  return labels[type as keyof typeof labels] || type
}

const handleClick = () => {
  emit('click')
}

const startEdit = () => {
  editing.value = true
  editValue.value = props.data.label
  nextTick(() => {
    editInput.value?.focus()
  })
}

const finishEdit = () => {
  if (editValue.value.trim()) {
    emit('updateLabel', editValue.value.trim())
  }
  editing.value = false
}
</script>

<style scoped>
.time-node {
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px;
  min-width: 120px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.time-node:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.time-node--selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.time-node__header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.time-node__icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: #f0f9ff;
  color: #409eff;
}

.time-node__title {
  flex: 1;
  font-weight: 500;
  font-size: 14px;
  color: #303133;
}

.time-node__content {
  text-align: center;
}

.time-node__type {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.time-node__value {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.time-node__handle {
  width: 8px;
  height: 8px;
  border: 2px solid #409eff;
  background: white;
}

.time-node__handle--target {
  left: -4px;
}

.time-node__handle--source {
  right: -4px;
}

/* 不同时间类型的颜色主题 */
.time-node--hour {
  border-color: #67c23a;
}

.time-node--hour .time-node__icon {
  background: #f0f9ff;
  color: #67c23a;
}

.time-node--day {
  border-color: #409eff;
}

.time-node--week {
  border-color: #e6a23c;
}

.time-node--week .time-node__icon {
  background: #fdf6ec;
  color: #e6a23c;
}

.time-node--month {
  border-color: #f56c6c;
}

.time-node--month .time-node__icon {
  background: #fef0f0;
  color: #f56c6c;
}

.time-node--quarter {
  border-color: #909399;
}

.time-node--quarter .time-node__icon {
  background: #f4f4f5;
  color: #909399;
}

.time-node--year {
  border-color: #9c27b0;
}

.time-node--year .time-node__icon {
  background: #f3e5f5;
  color: #9c27b0;
}
</style>
