<template>
  <div class="time-flow-chart">
    <!-- 简化的配置按钮 -->
    <div class="config-button-area">
      <el-button @click="openConnectionDialog" type="primary" size="large">
        <el-icon><Link /></el-icon>
        配置时间连接
      </el-button>

      <!-- 时间节点连接预览 -->
      <div v-if="connectionPreview.length > 0" class="connection-preview">
        <div class="preview-title">时间连接预览：</div>
        <div class="preview-connections">
          <div
            v-for="(connection, index) in connectionPreview"
            :key="index"
            class="connection-item"
          >
            <span class="time-type">{{ connection.startLabel }}</span>
            <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            <span class="time-type">{{ connection.endLabel }}</span>
          </div>
        </div>
      </div>

      <div v-else class="no-connection-tip">
        暂无时间连接，点击上方按钮进行配置
      </div>
    </div>

    <!-- 连接配置弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="时间周期连接配置"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="connection-dialog">
        <!-- 时间节点选择区域 -->
        <div class="node-selection">
          <h4>选择时间周期节点</h4>
          <div class="time-type-buttons">
            <el-button
              v-for="timeType in timeTypes"
              :key="timeType.value"
              @click="addTimeNode(timeType.value)"
              size="small"
              :disabled="hasTimeType(timeType.value)"
            >
              <el-icon>
                <component :is="timeType.icon" />
              </el-icon>
              {{ timeType.label }}
            </el-button>
          </div>
        </div>

        <!-- 流程图连接区域 -->
        <div class="flow-container">
          <VueFlow
            id="time-flow"
            key="time-flow"
            flow-key="time-flow"
            v-model:nodes="nodes"
            v-model:edges="edges"
            :node-types="nodeTypes"
            class="vue-flow"
            :default-viewport="{ zoom: 1 }"
            :min-zoom="0.2"
            :max-zoom="4"
          >
            <Background pattern="dots" :gap="20" />
            <Controls />
            <MiniMap />
          </VueFlow>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelConnection">取消</el-button>
          <el-button @click="clearAllNodes" type="warning">清空所有</el-button>
          <el-button @click="saveConnection" type="primary">保存连接</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 属性面板 -->
    <!-- <div v-if="selectedNode" class="property-panel">
      <div class="property-panel__header">
        <h4>节点属性</h4>
        <el-button @click="deleteSelectedNode" type="danger" size="small">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
      <div class="property-panel__content">
        <el-form label-width="60px" size="small">
          <el-form-item label="标签">
            <el-input 
              v-model="selectedNode.data.label" 
              @input="updateNodeLabel"
            />
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="selectedNode.data.timeType" @change="updateNodeType">
              <el-option 
                v-for="type in timeTypes" 
                :key="type.value"
                :label="type.label" 
                :value="type.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数值">
            <el-input 
              v-model="selectedNode.data.value" 
              @input="updateNodeValue"
              placeholder="可选"
            />
          </el-form-item>
        </el-form>
      </div>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, markRaw, watch, onMounted } from 'vue'
import {
  VueFlow,
  useVueFlow,
  type Node,
  type Edge,
  type Connection
} from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import {
  Clock,
  Calendar,
  Grid,
  Histogram,
  TrendCharts,
  Delete,
  Download,
  Close,
  Link,
  ArrowRight
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import TimeNode from './TimeNode.vue'

// 定义 props
interface Props {
  initialData?: {
    nodes: Node[];
    edges: Edge[];
    connectionPath?: string[];
  };
  periodEdges?: Array<{ start: number; end: number }>; // 新增：直接接收 periodEdges
}

const props = defineProps<Props>();

// 定义 emits
const emit = defineEmits<{
  flowChange: [data: { periodEdges: Array<{ start: number; end: number }> }];
}>();

// 时间类型配置
const timeTypes = [
  { value: 'hour', label: '小时', icon: markRaw(Clock) },
  { value: 'day', label: '天', icon: markRaw(Calendar) },
  { value: 'week', label: '周', icon: markRaw(Grid) },
  { value: 'month', label: '月', icon: markRaw(Calendar) },
  { value: 'quarter', label: '季', icon: markRaw(Histogram) },
  { value: 'year', label: '年', icon: markRaw(TrendCharts) }
]

// 时间类型到数字ID的映射
const timeTypeToIdMap: Record<string, number> = {
  'hour': 7,
  'day': 12,
  'week': 13,
  'month': 14,
  'quarter': 15,
  'year': 17
}

// 节点类型注册
const nodeTypes = {
  timeNode: markRaw(TimeNode)
}

// 使用独立的 VueFlow 实例
const {
  onConnect: timeOnConnect,
  onNodesChange: timeOnNodesChange,
  onEdgesChange: timeOnEdgesChange,
  onNodeClick: timeOnNodeClick
} = useVueFlow('time-flow');

// 响应式数据
const nodes = ref<Node[]>([])
const edges = ref<Edge[]>([])
const selectedNode = ref<Node | null>(null)
const nodeIdCounter = ref(1)

// 弹窗相关
const dialogVisible = ref(false)

// 连接预览数据
const connectionPreview = ref<Array<{
  startLabel: string;
  endLabel: string;
  start: number;
  end: number;
}>>([]);

// 监听 periodEdges 变化（优先用于回显）
watch(() => props.periodEdges, (newPeriodEdges) => {
  console.log('TimeFlowChart 接收到 periodEdges:', newPeriodEdges);
  if (newPeriodEdges && newPeriodEdges.length > 0) {
    console.log('从 periodEdges 回显连接预览:', newPeriodEdges);
    // 直接从 periodEdges 生成预览
    const preview = generatePreviewFromPeriodEdges(newPeriodEdges);
    console.log('生成的预览数据:', preview);
    connectionPreview.value = preview;
  } else {
    console.log('没有 periodEdges 数据，清空预览');
    // 如果没有 periodEdges，清空预览
    connectionPreview.value = [];
  }
}, { immediate: true, deep: true });

// 监听初始数据变化
watch(() => props.initialData, (newData) => {
  if (newData) {
    nodes.value = newData.nodes || [];
    edges.value = newData.edges || [];

    // 如果没有 periodEdges，才从流程图数据生成预览
    if (!props.periodEdges || props.periodEdges.length === 0) {
      connectionPreview.value = generateConnectionPreview(edges.value, nodes.value);
    }

    // 更新节点计数器
    if (nodes.value.length > 0) {
      const maxId = Math.max(...nodes.value.map(node => {
        const match = node.id.match(/node-(\d+)/);
        return match ? parseInt(match[1]) : 0;
      }));
      nodeIdCounter.value = maxId + 1;
    }
  }
}, { immediate: true, deep: true });

// 监听节点和边的变化，发射转换后的 periodEdges 事件
watch([nodes, edges], () => {
  const periodEdges = convertEdgesToPeriodEdges(edges.value, nodes.value);

  // 生成连接预览
  connectionPreview.value = generateConnectionPreview(edges.value, nodes.value);

  emit('flowChange', {
    periodEdges: periodEdges
  });
}, { deep: true });

// 添加时间节点
const addTimeNode = (timeType: string) => {
  const typeConfig = timeTypes.find(t => t.value === timeType)
  const newNode: Node = {
    id: `node-${nodeIdCounter.value++}`,
    type: 'timeNode',
    position: { 
      x: Math.random() * 400 + 100, 
      y: Math.random() * 300 + 100 
    },
    data: {
      timeType,
      label: typeConfig?.label || timeType,
      value: ''
    }
  }
  nodes.value.push(newNode)
  ElMessage.success(`已添加${typeConfig?.label}节点`)
}

// 使用独立实例的事件处理
timeOnNodesChange((changes: any[]) => {
  // 处理时间流程图的节点变化
  console.log('Time flow nodes changed:', changes);
});

timeOnEdgesChange((changes: any[]) => {
  // 处理时间流程图的边变化
  console.log('Time flow edges changed:', changes);
});

timeOnConnect((connection: Connection) => {
  try {
    // 验证连接的合理性
    if (validateConnection(connection)) {
      const newEdge: Edge = {
        id: `edge-${Date.now()}`,
        source: connection.source,
        target: connection.target,
        sourceHandle: connection.sourceHandle,
        targetHandle: connection.targetHandle
      };
      edges.value.push(newEdge);
      ElMessage.success('节点连接成功')
    } else {
      ElMessage.error('连接不合理，请检查时间周期的逻辑关系')
    }
  } catch (error) {
    console.error('连接失败:', error)
    ElMessage.error('节点连接失败')
  }
});

timeOnNodeClick((event: any) => {
  selectedNode.value = event.node;
});

// 验证连接的合理性（从小到大连接）
const validateConnection = (connection: Connection): boolean => {
  const sourceNode = nodes.value.find(n => n.id === connection.source);
  const targetNode = nodes.value.find(n => n.id === connection.target);

  if (!sourceNode || !targetNode) return false;

  const sourceType = sourceNode.data?.timeType;
  const targetType = targetNode.data?.timeType;

  // 定义时间周期的层级关系（从小到大）
  const timeHierarchy = ['hour', 'day', 'week', 'month', 'quarter', 'year'];
  const sourceIndex = timeHierarchy.indexOf(sourceType);
  const targetIndex = timeHierarchy.indexOf(targetType);

  // 只允许从小周期连接到大周期
  return sourceIndex >= 0 && targetIndex >= 0 && sourceIndex < targetIndex;
};



// 更新节点标签
const updateNodeLabel = () => {
  if (selectedNode.value) {
    const nodeIndex = nodes.value.findIndex(n => n.id === selectedNode.value!.id)
    if (nodeIndex !== -1) {
      nodes.value[nodeIndex].data.label = selectedNode.value.data.label
    }
  }
}

// 更新节点类型
const updateNodeType = () => {
  if (selectedNode.value) {
    const nodeIndex = nodes.value.findIndex(n => n.id === selectedNode.value!.id)
    if (nodeIndex !== -1) {
      nodes.value[nodeIndex].data.timeType = selectedNode.value.data.timeType
    }
  }
}

// 更新节点数值
const updateNodeValue = () => {
  if (selectedNode.value) {
    const nodeIndex = nodes.value.findIndex(n => n.id === selectedNode.value!.id)
    if (nodeIndex !== -1) {
      nodes.value[nodeIndex].data.value = selectedNode.value.data.value
    }
  }
}

// 删除选中节点
const deleteSelectedNode = () => {
  if (selectedNode.value) {
    const nodeId = selectedNode.value.id
    nodes.value = nodes.value.filter(n => n.id !== nodeId)
    edges.value = edges.value.filter(e => e.source !== nodeId && e.target !== nodeId)
    selectedNode.value = null
    ElMessage.success('节点已删除')
  }
}

// 清空所有
const clearAll = () => {
  nodes.value = []
  edges.value = []
  selectedNode.value = null
  ElMessage.success('已清空所有节点')
}

// 导出数据
const exportData = () => {
  const data = {
    nodes: nodes.value,
    edges: edges.value
  }
  console.log('导出数据:', data)
  ElMessage.success('数据已导出到控制台')
}

// 弹窗相关方法
const openConnectionDialog = () => {
  dialogVisible.value = true;
};

const cancelConnection = () => {
  dialogVisible.value = false;
};

const saveConnection = () => {
  dialogVisible.value = false;
  ElMessage.success('时间连接配置已保存');
};

const clearAllNodes = () => {
  nodes.value = [];
  edges.value = [];
  selectedNode.value = null;
  ElMessage.success('已清空所有节点');
};

// 检查是否已存在某种时间类型
const hasTimeType = (timeType: string): boolean => {
  return nodes.value.some(node => node.data?.timeType === timeType);
};

// 将流程图边转换为 periodEdges 格式
const convertEdgesToPeriodEdges = (edges: Edge[], nodes: Node[]) => {
  return edges.map(edge => {
    // 找到源节点和目标节点
    const sourceNode = nodes.find(node => node.id === edge.source);
    const targetNode = nodes.find(node => node.id === edge.target);

    if (!sourceNode || !targetNode) {
      console.warn('找不到对应的节点:', edge);
      return null;
    }

    // 获取时间类型并转换为数字 ID
    const startId = timeTypeToIdMap[sourceNode.data?.timeType];
    const endId = timeTypeToIdMap[targetNode.data?.timeType];

    if (!startId || !endId) {
      console.warn('无效的时间类型:', sourceNode.data?.timeType, targetNode.data?.timeType);
      return null;
    }

    return {
      start: startId,
      end: endId
    };
  }).filter(edge => edge !== null); // 过滤无效边
};

// 生成连接预览数据
const generateConnectionPreview = (edges: Edge[], nodes: Node[]) => {
  return edges.map(edge => {
    // 找到源节点和目标节点
    const sourceNode = nodes.find(node => node.id === edge.source);
    const targetNode = nodes.find(node => node.id === edge.target);

    if (!sourceNode || !targetNode) {
      return null;
    }

    // 获取时间类型标签
    const sourceType = timeTypes.find(t => t.value === sourceNode.data?.timeType);
    const targetType = timeTypes.find(t => t.value === targetNode.data?.timeType);

    if (!sourceType || !targetType) {
      return null;
    }

    return {
      startLabel: sourceType.label,
      endLabel: targetType.label,
      start: timeTypeToIdMap[sourceNode.data?.timeType],
      end: timeTypeToIdMap[targetNode.data?.timeType]
    };
  }).filter(preview => preview !== null);
};

// 直接从 periodEdges 生成连接预览（用于回显）
const generatePreviewFromPeriodEdges = (periodEdges: Array<{ start: number; end: number }>) => {
  if (!periodEdges || periodEdges.length === 0) {
    return [];
  }

  return periodEdges.map(edge => {
    // 根据数字ID找到对应的时间类型
    const startTimeType = Object.keys(timeTypeToIdMap).find(key => timeTypeToIdMap[key] === edge.start);
    const endTimeType = Object.keys(timeTypeToIdMap).find(key => timeTypeToIdMap[key] === edge.end);

    if (!startTimeType || !endTimeType) {
      console.warn('无法找到对应的时间类型:', edge);
      return null;
    }

    // 获取时间类型标签
    const startType = timeTypes.find(t => t.value === startTimeType);
    const endType = timeTypes.find(t => t.value === endTimeType);

    if (!startType || !endType) {
      console.warn('无法找到时间类型配置:', startTimeType, endTimeType);
      return null;
    }

    return {
      startLabel: startType.label,
      endLabel: endType.label,
      start: edge.start,
      end: edge.end
    };
  }).filter(preview => preview !== null);
};



// 暴露给父组件的方法和数据
defineExpose({
  nodes,
  edges,
  addTimeNode,
  clearAll,
  openConnectionDialog,
  getPeriodEdges: () => convertEdgesToPeriodEdges(edges.value, nodes.value)
})
</script>

<style scoped>
.time-flow-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.config-button-area {
  padding: 20px;
  text-align: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.connection-preview {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  min-height: auto;
  max-height: 300px; /* 最大高度限制 */
  overflow-y: auto; /* 超出时滚动 */
  transition: all 0.3s ease;
}

.preview-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.preview-title::before {
  content: "🔗";
  font-size: 16px;
}

.preview-connections {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.connection-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 10px 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
  position: relative;
}

.connection-item:hover {
  box-shadow: 0 2px 6px rgba(0,0,0,0.15);
  transform: translateY(-1px);
}

.time-type {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  padding: 4px 10px;
  background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
  border-radius: 4px;
  border: 1px solid #dee2e6;
  min-width: 40px;
  text-align: center;
}

.arrow-icon {
  color: #28a745;
  font-size: 16px;
  font-weight: bold;
}

.no-connection-tip {
  margin-top: 16px;
  padding: 20px;
  text-align: center;
  color: #6c757d;
  font-size: 14px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .connection-preview {
    padding: 12px;
  }

  .connection-item {
    padding: 8px 12px;
    gap: 8px;
  }

  .time-type {
    font-size: 12px;
    padding: 3px 8px;
  }
}

.connection-dialog {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.node-selection h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.time-type-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.flow-container {
  height: 400px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.vue-flow {
  height: 100%;
}



.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.property-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 280px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 1000;
}

.property-panel__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.property-panel__header h4 {
  margin: 0;
  color: #303133;
}

.property-panel__content {
  padding: 16px;
}
</style>
