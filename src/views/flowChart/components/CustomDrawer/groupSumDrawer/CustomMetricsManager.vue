<template>
  <div class="custom-metrics-manager">
    <!-- 常见指标选择 -->
    <div class="common-metrics-section">
      <h4>常见指标（可多选）</h4>
      <el-checkbox-group v-model="selectedFunctions" @change="handleFunctionsChange">
        <el-checkbox 
          v-for="metric in commonMetrics" 
          :key="metric.key" 
          :value="metric.key"
          :label="metric.label"
        />
      </el-checkbox-group>
    </div>

    <!-- 自定义指标管理 -->
    <div class="custom-formulas-section">
      <div class="section-header">
        <h4>自定义字段</h4>
        <el-button type="primary" size="small" @click="addCustomFormula">
          <el-icon><Plus /></el-icon>
          新增
        </el-button>
      </div>
      
      <div class="formulas-list">
        <div 
          v-for="(formula, index) in formulas" 
          :key="index"
          class="formula-item"
        >
          <div class="formula-fields">
            <el-form-item label="字段名称" :prop="`formulas.${index}.name`">
              <el-input 
                v-model="formula.name" 
                placeholder="请输入指标名称"
                @input="handleFormulaChange"
              />
            </el-form-item>
            
            <el-form-item label="字段标识" :prop="`formulas.${index}.label`">
              <el-input 
                v-model="formula.label" 
                placeholder="请输入标识"
                @input="handleFormulaChange"
              />
            </el-form-item>

            <el-form-item label="前端类型" :prop="`formulas.${index}.frontedType`">
              <el-input 
                v-model="formula.frontedType" 
                placeholder="请输入类型"
                @input="handleFormulaChange"
              />
            </el-form-item>

            <el-form-item label="字段类型" :prop="`formulas.${index}.fieldType`">
              <el-input 
                v-model="formula.fieldType" 
                placeholder="请输入类型"
                @input="handleFormulaChange"
              />
            </el-form-item>
            
            <el-form-item label="字段公式" :prop="`formulas.${index}.formula`">
              <el-input 
                v-model="formula.formula" 
                type="textarea"
                :rows="2"
                placeholder="请输入指标公式，如：(max()-min())/max()"
                @input="handleFormulaChange"
              />
            </el-form-item>
          </div>
          
          <div class="formula-actions">
            <el-button 
              type="primary" 
              size="small" 
              @click="editFormula(index)"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteFormula(index)"
            >
              删除
            </el-button>
          </div>
        </div>
        
        <div v-if="formulas.length === 0" class="empty-state">
          暂无自定义指标，点击上方"新增"按钮添加
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { COMMON_METRICS, type CommonMetricParam } from '@/utils/globalField'
import type { CustomFormulaParam } from '@/types/param-rules'

// Props
interface Props {
  modelValue?: {
    functions?: string[];
    formulas?: CustomFormulaParam[];
  };
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: {
    functions: string[];
    formulas: CustomFormulaParam[];
  }];
  'change': [value: {
    functions: string[];
    formulas: CustomFormulaParam[];
  }];
}>();

// 常见指标数据
const commonMetrics = ref<CommonMetricParam[]>(COMMON_METRICS);

// 选中的常见指标
const selectedFunctions = ref<string[]>([]);

// 自定义指标公式
const formulas = ref<CustomFormulaParam[]>([]);

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    selectedFunctions.value = newValue.functions || [];
    formulas.value = newValue.formulas || [];
  }
}, { immediate: true, deep: true });

// 处理常见指标变化
const handleFunctionsChange = () => {
  emitChange();
};

// 处理自定义指标变化
const handleFormulaChange = () => {
  emitChange();
};

// 添加自定义指标
const addCustomFormula = () => {
  formulas.value.push({
    name: '',
    label: '',
    frontedType: '',
    fieldType: '',
    formula: ''
  });
  emitChange();
};

// 编辑指标（预留功能，当前直接编辑）
const editFormula = (index: number) => {
  // 可以在这里添加编辑弹窗逻辑
  console.log('编辑指标:', index);
};

// 删除指标
const deleteFormula = (index: number) => {
  formulas.value.splice(index, 1);
  emitChange();
};

// 发射变化事件
const emitChange = () => {
  const value = {
    functions: selectedFunctions.value,
    formulas: formulas.value
  };
  emit('update:modelValue', value);
  emit('change', value);
};

// 获取选中的常见指标详情
const getSelectedFunctionsDetails = computed(() => {
  return selectedFunctions.value.map(key => {
    const metric = commonMetrics.value.find(m => m.key === key);
    return metric ? {
      id: metric.id,
      key: metric.key,
      label: metric.label,
      dataType: metric.dataType
    } : null;
  }).filter(Boolean);
});

// 暴露方法给父组件
defineExpose({
  getSelectedFunctionsDetails,
  selectedFunctions,
  formulas
});
</script>

<style lang="scss" scoped>
.custom-metrics-manager {
  .common-metrics-section {
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 600;
    }
    
    .el-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      
      .el-checkbox {
        margin-right: 0;
      }
    }
  }
  
  .custom-formulas-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h4 {
        margin: 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }
    }
    
    .formulas-list {
      .formula-item {
        display: flex;
        gap: 16px;
        padding: 16px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        margin-bottom: 12px;
        background: #fafafa;
        
        .formula-fields {
          flex: 1;
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
          
          .el-form-item:nth-child(3) {
            grid-column: 1 / -1;
          }
          
          :deep(.el-form-item) {
            margin-bottom: 0;
            
            .el-form-item__label {
              font-size: 12px;
              color: #606266;
              margin-bottom: 4px;
            }
          }
        }
        
        .formula-actions {
          display: flex;
          flex-direction: column;
          gap: 8px;
          align-items: flex-start;
          padding-top: 24px;
        }
      }
      
      .empty-state {
        text-align: center;
        color: #909399;
        font-size: 14px;
        padding: 40px 20px;
        border: 2px dashed #e4e7ed;
        border-radius: 8px;
        background: #fafafa;
      }
    }
  }
}
</style>
