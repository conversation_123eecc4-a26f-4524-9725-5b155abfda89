<template>
  <div class="page page-table">
    <el-form
      class="form-style form-table-style"
      ref="ruleFormRef"
      label-position="top"
      require-asterisk-position="right"
      label-width="auto"
      :model="modelInputData"
      :rules="modelInputDataRules"
      :show-message="false"
    >
      <el-form-item label="节点名称" prop="name">
        <el-input v-model="modelInputData.name" maxlength="20"></el-input>
        <div class="form-item-tips">指定任何有意义且能描述本节点的说明</div>
      </el-form-item>
      <el-form-item label="节点ID" prop="id">
        <el-input v-model="modelInputData.id" disabled></el-input>
        <div class="form-item-tips">节点ID不能重复</div>
      </el-form-item>
      <el-form-item label="模型选择" prop="syncModel">
        <el-input
          v-model="modelInputData.syncModel"
          placeholder="请输入模型名称"
          @change="inputChange"
        ></el-input>
        <div class="form-item-tips">
          输入所要选择的模型名称，下方表格数据由该模型进行查询
        </div>
      </el-form-item>
      <el-form-item label="起始时间" prop="startTime">
        <el-date-picker
          v-model="modelInputData.startTime"
          type="date"
          placeholder="选择起始时间"
          value-format="x"
          style="width: 100%"
        />
        <div class="form-item-tips" style="width: 100%">选择计算的起始时间</div>
      </el-form-item>
      <el-form-item label="最大延迟时间" prop="latenessDay">
        <el-radio-group v-model="modelInputData.latenessDay">
          <template v-for="item in DelayTimeOptions">
            <el-radio :value="item.value">{{ item.label }}</el-radio>
          </template>
        </el-radio-group>
        <div class="form-item-tips">选择计算的最大延迟时间</div>
      </el-form-item>
      <el-form-item prop="tableData" class="form-table-height">
        <el-table :data="tableData" scrollbar-always-on class="fullheight">
          <template v-for="item in fieldTableHeader">
            <el-table-column
              v-bind="item"
              show-overflow-tooltip
            ></el-table-column>
          </template>
        </el-table>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
// 引入通用的校验规则
import { getFormRules } from "@/utils/common";

const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<AllInputParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    modelInputData.value = cloneDeep(inputData.value);
    modelInputData.value.to = inputData.value.id;
    inputChange();
    // 延迟进行表单校验的清空操作才会生效,处理下拉框的change导致进入弹窗的校验触发
    setTimeout(() => {
      clearValidate();
    }, 10);
  },
  {
    immediate: true,
  }
);

const modelInputData = ref<AllInputParam>({
  id: "",
  name: "",
});

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 定义页面名称
const componentPageName = "modelInputSetting";

// 用于监听菜单是否进行切换到指定页面
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (oldVal === componentPageName) {
      flowChartStore.updateTempData(
        inputData.value.nodeType,
        modelInputData.value
      );
    }
  }
);

// 表单校验规则
import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const modelInputDataRules = {
  id: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  startTime: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  latenessDay: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  syncModel: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
};

import { fieldTableHeader } from "@/utils/globalField";

const tableData = ref<FieldTableParam[]>([]);

import customApi from "@/api/custom";

// 获取表格字段数据
async function queryFieldTableData(type?: string) {
  if (!type) return;
  try {
    // 查询字段表数据
    const res = await customApi.queryModelFields({ modelLabel: type });
    tableData.value = res.data;
  } catch (err) {
    console.log(err);
  }
}

// 延迟时间选项--按照陈练练要求添加该字段并写死
const DelayTimeOptions: OptionsParam[] = [
  {
    label: "1天",
    value: 1,
  },
  {
    label: "2天",
    value: 2,
  },
  {
    label: "3天",
    value: 3,
  },
  {
    label: "4天",
    value: 4,
  },
  {
    label: "5天",
    value: 5,
  },
  {
    label: "6天",
    value: 6,
  },
  {
    label: "7天",
    value: 7,
  },
];

/**
 * @description: 输入框内容改变
 * @return {*}
 */
function inputChange() {
  tableData.value.length = 0;

  if (!modelInputData.value.syncModel) return;
  queryFieldTableData(modelInputData.value.syncModel);
}

// 进行保存校验
async function getNewData() {
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    modelInputData.value,
    "基本配置"
  );
  return childRuleBool;
}

/**
 * @description: 判断输入源是否修改导致字段进行修改
 * @return {*}
 */
function checkFieldUpdate() {
  if (!inputData.value.syncModel) return false;
  return modelInputData.value.syncModel !== inputData.value.syncModel;
}

// 关闭弹窗清楚校验
function clearValidate() {
  ruleFormRef.value?.clearValidate();
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
  checkFieldUpdate,
});
</script>

<style lang="scss" scoped>
.page {
  .form-table-height {
    height: calc(100% - 500px);
  }
}
</style>
