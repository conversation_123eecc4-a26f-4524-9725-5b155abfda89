<template>
  <ComDataPreview
    ref="dataPreviewRef"
    :inputData="inputData"
    :isShowDebug="true"
    :componentPageName="componentPageName"
    v-bind="$attrs"
  ></ComDataPreview>
</template>

<script setup lang="ts">
import ComDataPreview from "../../ComDataPreview/index.vue";

// 定义页面名称
const componentPageName = "modelInputDataPreview";

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<AllInputParam>;

const dataPreviewRef = ref();

async function getNewData() {
  const result = await dataPreviewRef.value?.getNewData();
  return result;
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
});
</script>

<style scoped></style>
