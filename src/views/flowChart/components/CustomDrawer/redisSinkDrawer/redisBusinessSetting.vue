<template>
  <div class="page page-table">
    <el-form
      class="form-style form-table-style"
      ref="ruleFormRef"
      label-position="top"
      require-asterisk-position="right"
      label-width="auto"
      :model="redisOutputData"
      :rules="redisOutputDataRules"
      :show-message="false"
    >
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="节点名称" prop="name">
            <el-input v-model="redisOutputData.name" maxlength="20"></el-input>
            <div class="form-item-tips">指定任何有意义且能描述本节点的说明</div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="节点ID" prop="id">
            <el-input v-model="redisOutputData.id" disabled></el-input>
            <div class="form-item-tips">节点ID不能重复</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="同步模型" prop="syncMethod">
            <el-select v-model="redisOutputData.syncMethod">
              <el-option
                v-for="item in syncMethodOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item
            label="模型选择"
            prop="redisTableName"
            v-if="redisOutputData.syncMethod == '1'"
          >
            <el-input
              v-model="redisOutputData.redisTableName"
              placeholder="请输入模型名称"
              @change="inputChange"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item
        prop="tableData"
        v-if="redisOutputData.syncMethod == '1'"
        class="form-table-height"
      >
        <div class="table_describe">
          输出表和同步表单字段间对应的存储关系如下：
        </div>
        <el-table
          :data="redisOutputData.modelAssociation"
          scrollbar-always-on
          style="height: calc(100% - 48px); min-height: 140px"
        >
          <el-table-column label="上游字段" min-width="150">
            <template #default="scope">
              <el-select
                v-model="scope.row.selectedAField"
                placeholder="请选择"
                disabled
              >
                <el-option
                  v-for="option in tableOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="运算符" width="100" align="center">
            =
          </el-table-column>
          <el-table-column label="模型字段" min-width="150">
            <template #default="scope">
              <el-select
                filterable
                v-model="scope.row.selectedBLabel"
                placeholder="请选择"
                value-key="label"
                @change="handleSelectBChange(scope.row, scope.$index)"
                clearable
                @focus="getTypeModel($event, scope.row.selectedAtype)"
                :disabled="!isApiCompleted"
              >
                <el-option
                  v-for="option in modelFieldOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.label"
                  :disabled="isOptionsDisabled(option)"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
// 引入通用的校验规则
import {
  getFormRules,
  getConnectData,
  getNodeFrom,
  getResetField,
} from "@/utils/common";

const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
}>();

// 传递子组件的emit
const emit = defineEmits(["updateNodeInfo", "closeDrawer"]);

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<AllOutputParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    redisOutputData.value = cloneDeep(inputData.value);
    redisOutputData.value.from = cloneDeep(inputData.value.from) || [];
    initData();
    refreshOptions();
    // 延迟进行表单校验的清空操作才会生效,处理下拉框的change导致进入弹窗的校验触发
    setTimeout(() => {
      clearValidate();
    }, 10);
  },
  {
    immediate: true,
  }
);

function initData() {
  redisOutputData.value.syncMethod ||= "1";
  redisOutputData.value.modelAssociation ??= [];
  redisOutputData.value.filterTable ??= [];
  isApiCompleted.value = false;
}

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 定义页面名称
const componentPageName = "redisSinkSetting";

// 用于监听菜单是否进行切换到指定页面  --用于处理多页面时候的情况
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (oldVal === componentPageName) {
      flowChartStore.updateTempData(
        inputData.value.nodeType,
        redisOutputData.value
      );
    }
  }
);

import { useVueFlow } from "@vue-flow/core";

const { getIncomers } = useVueFlow();

// 数据所在节点
const locatedNodeOptions = ref<ComputerOptionsParam[]>([]);

// 回显判断是否重新获取字段信息
async function refreshOptions() {
  getlocatedData();
  await loadField();
  if (
    redisOutputData.value.syncMethod == "1" &&
    redisOutputData.value.redisTableName
  ) {
    await getModelField(redisOutputData.value.redisTableName);
  }
}

// 获取与之连接数据的节点数据
function getlocatedData() {
  locatedNodeOptions.value.length = 0;
  // 获取当前节点的所有入边
  const incomers = getIncomers(inputData.value.id);
  locatedNodeOptions.value = getResetField(incomers);
}

// 上一个算子获取表格字段
const tableOptions = ref<NodeOptionsParam[]>([]);

// 模型字段获取表格字段
const modelFieldOptions = ref<NodeOptionsParam[]>([]);

const modelAssociation = ref<ModelAssociationTableParam[]>([]);

async function loadField() {
  tableOptions.value.length = 0;
  modelAssociation.value.length = 0;
  // 调用方法获取字段数据
  const resData = (await getConnectData(
    locatedNodeOptions.value
  )) as ConnectDataParam;
  const { getTableData, filterData } = resData;
  redisOutputData.value.dataStream = filterData?.dataStream;
  if (isEmpty(getTableData)) return;
  redisOutputData.value.filterTable = cloneDeep(getTableData);
  getTableData.forEach((item: FieldTableParam) => {
    tableOptions.value.push({
      value: item.field,
      label: item.description + "（" + item.field + "）",
      key: item.frontedType,
    });
    modelAssociation.value?.push({
      selectedAField: item.field as string,
      selectedAtype: item.fieldType,
      selectedBField: "",
      selectedBtype: "",
      selectedBLabel: "",
    });
  });

  if (isEmpty(redisOutputData.value.modelAssociation)) {
    redisOutputData.value.modelAssociation = cloneDeep(modelAssociation.value);
    modelFieldOptions.value.length = 0;
    allModelFieldOptions.value.length = 0;
  }
}

// 表单校验规则
import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const redisOutputDataRules = {
  id: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  syncMethod: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  redisTableName: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
};

const redisOutputData = ref<AllOutputParam>({
  id: "",
  name: "",
});

const syncMethodOptions = [
  {
    value: "1",
    label: "同步到已有模型",
  },
];

import customApi from "@/api/custom";

const allModelFieldOptions = ref<RedisModelParam[]>([]);

// 用于判断接口是否请求完成
const isApiCompleted = ref(false);

// 模型字段数据清空并置灰
function clearModelField() {
  isApiCompleted.value = false;
  modelFieldOptions.value.length = 0;
  allModelFieldOptions.value.length = 0;
}

// 获取模型字段
async function getModelField(modelLabel: number | string) {
  // 获取数据之前先清空原先的数据
  clearModelField();

  try {
    const res = await customApi.getRedisMeta({
      modelLabel: modelLabel,
    });

    const length = res.data?.length;
    if (tableOptions.value.length < length) {
      return ElMessage.error(
        "该模型字段数量超过当前上游算子字段，部分字段无法正常映射"
      );
    }

    allModelFieldOptions.value = cloneDeep(res.data);
    isApiCompleted.value = true;
  } catch (err) {
    console.log(err);
  }
}

function getTypeModel(event: FocusEvent, type: string) {
  // 接口未成功获取之前禁止聚焦
  if (!isApiCompleted.value) {
    event.preventDefault();
    (event.target as HTMLElement)?.blur();
    return;
  }

  const data = allModelFieldOptions.value || [];
  modelFieldOptions.value = data.map((item) => {
    const { propertyLabel, dataType } = item;
    return {
      value: propertyLabel,
      label: propertyLabel,
      key: dataType as string,
    };
  });
}

// 模型改变
function inputChange() {
  // 清空上一次的选择;
  redisOutputData.value.modelAssociation = cloneDeep(modelAssociation.value);
  if (redisOutputData.value.redisTableName) {
    getModelField(redisOutputData.value.redisTableName);
  } else {
    // 当输入值为空的时候
    clearModelField();
  }
}

// 模型字段选择
function handleSelectBChange(row: ModelAssociationTableParam, index: number) {
  const data = modelFieldOptions.value.find(
    (item) => item.label === row.selectedBLabel
  );
  if (redisOutputData.value.modelAssociation) {
    redisOutputData.value.modelAssociation[index].selectedBField =
      (data?.value as string) || "";
    redisOutputData.value.modelAssociation[index].selectedBtype =
      data?.key || "";
    redisOutputData.value.modelAssociation[index].selectedBLabel =
      row.selectedBLabel || "";
  }
}

// 定义选中的模型字段数据
const allmodelField = ref<(string | number)[]>([]);

// 监听数据变化
watch(
  () => redisOutputData.value.modelAssociation,
  (val) => {
    allmodelField.value.length = 0;
    val?.forEach((item) => {
      if (item.selectedBLabel) {
        allmodelField.value.push(item.selectedBLabel);
      }
    });
  },
  {
    deep: true,
  }
);

// 进行限制处理，已选择的不允许再次选择
function isOptionsDisabled(data: OptionsParam) {
  return allmodelField.value.includes(data.label);
}

// 进行保存校验
async function getNewData() {
  // 关联字段规则检查
  if (!(await checkCondition())) {
    return false;
  }

  redisOutputData.value.from = getNodeFrom(locatedNodeOptions.value);

  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    redisOutputData.value,
    "基本配置"
  );
  return childRuleBool;
}

async function checkCondition() {
  // 去除表格中的空数据
  const data = redisOutputData.value.modelAssociation?.filter(
    (item) => item.selectedAField && item.selectedBLabel
  );
  if (!data || data.length === 0) {
    ElMessage.error("请至少添加一行关联字段");
    return false;
  }

  if (allModelFieldOptions.value.length !== allmodelField.value.length) {
    ElMessage.error("模型字段未全部映射，请检查");
    return false;
  }
  return true;
}

// 关闭弹窗清楚校验
function clearValidate() {
  ruleFormRef.value?.clearValidate();
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
});
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
  .form-table-height {
    height: calc(100% - 335px);
  }
  .search-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-top: 8px;
    width: 100%;
    border-top: 2px solid #e8e8e8;
  }
  .table_describe {
    font-weight: 400;
    font-size: 14px;
    color: #4e5969;
    margin-bottom: 16px;
  }
}
</style>
