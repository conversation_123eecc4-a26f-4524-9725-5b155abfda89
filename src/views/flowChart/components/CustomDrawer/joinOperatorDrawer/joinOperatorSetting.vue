<template>
  <ComStepPage
    ref="comStepRef"
    :stepData="stepData"
    :componentPageName="componentPageName"
    v-bind="$attrs"
  ></ComStepPage>
</template>

<script setup lang="ts">
import ComStepPage from "../../ComStepPage/index.vue";
// 基本属性
import basicAttribute from "./joinSetting/basicAttribute.vue";
// 输入字段
import tableAConfig from "./joinSetting/tableAConfig.vue";
// B表字段配置
import tableBConfig from "./joinSetting/tableBConfig.vue";

// 定义页面名称
const componentPageName = "joinOperatorSetting";

const stepData: StepParam[] = [
  {
    title: "基本属性",
    component: basicAttribute,
    index: 0,
  },
  {
    title: "A表字段配置",
    component: tableAConfig,
    index: 1,
  },
  {
    title: "B表字段配置",
    component: tableBConfig,
    index: 2,
  },
];

const comStepRef = ref();

// 判断页面中多个步骤条的校验是否通过
async function getNewData() {
  if (!comStepRef.value) return false;
  const ruleBool = await comStepRef.value.getNewData();
  return ruleBool;
}

// 判断页面中多个步骤条的校验是否通过
async function checkFieldUpdate() {
  if (!comStepRef.value) return false;
  const ruleBool = await comStepRef.value.checkFieldUpdate();
  return ruleBool;
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
  checkFieldUpdate,
});
</script>

<style lang="scss" scoped></style>
