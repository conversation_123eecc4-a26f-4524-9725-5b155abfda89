<template>
  <div class="page">
    <el-form
      class="form-style"
      ref="ruleFormRef"
      label-position="top"
      require-asterisk-position="right"
      label-width="auto"
      :model="joinOperatorData"
      :rules="joinOperatorDataRules"
      :show-message="false"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="节点名称" prop="name">
            <el-input v-model="joinOperatorData.name" maxlength="20"></el-input>
            <div class="form-item-tips">指定任何有意义且能描述本节点的说明</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="节点ID" prop="id">
            <el-input v-model="joinOperatorData.id" disabled></el-input>
            <div class="form-item-tips">节点ID不能重复</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="join模式" prop="joinType">
            <el-select v-model="joinOperatorData.joinType">
              <el-option
                v-for="item in joinTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="form-item-tips">
              指定是leftjoin还是innerjoin(rightjoin)方式请调整A、B表的位置
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="A流" prop="streamAM">
            <el-select
              v-model="joinOperatorData.streamAM"
              clearable
              @change="handleFieldChange(joinOperatorData.streamAM, 'A')"
            >
              <el-option
                v-for="item in streamAOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="isOptionsStearm(item, 'A')"
              />
            </el-select>
            <div class="form-item-tips">
              指定A表数据所在节点（注：A流将作为主流）
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="B流" prop="streamBM">
            <el-select
              v-model="joinOperatorData.streamBM"
              clearable
              @change="handleFieldChange(joinOperatorData.streamBM, 'B')"
            >
              <el-option
                v-for="item in streamBOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="isOptionsStearm(item, 'B')"
              />
            </el-select>
            <div class="form-item-tips">指定B表数据所在节点</div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="A、B流关联条件" prop="joinCondition">
            <el-input
              v-model="joinOperatorData.joinCondition"
              disabled
            ></el-input>
            <div class="form-item-tips">
              条件示例（字段不分大小写）：a.id=b.id and a.userid=b.userid
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="joinOperatorData.remark"
              type="textarea"
              :rows="1"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item prop="tableData">
        <div style="margin-bottom: 20px; float: right">
          <el-button @click="addAssociation">+ 新增条件</el-button>
          <el-button @click="refreshOptions">刷新</el-button>
        </div>
        <el-table
          :data="joinOperatorData.associationTableData"
          scrollbar-always-on
        >
          <el-table-column label="A表字段" min-width="150">
            <template #default="scope">
              <el-select
                v-model="scope.row.selectedAField"
                placeholder="请选择"
              >
                <el-option
                  v-for="option in tableAOptions"
                  :key="option.field"
                  :label="option.label"
                  :value="option.field"
                  :disabled="isOptionsDisabledForDateField(option, 'A', scope.$index)"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="运算符" width="120" align="center">
            <template #default="scope">
              <el-select
                v-model="scope.row.operator"
                placeholder="选择运算符"
                :disabled="!scope.row.selectedAField || !scope.row.selectedBField"
                @change="handleOperatorChange"
              >
                <el-option
                  v-for="option in getAvailableOperators(scope.row.selectedAField, scope.row.selectedBField)"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="B表字段" min-width="150">
            <template #default="scope">
              <el-select
                v-model="scope.row.selectedBField"
                placeholder="请选择"
              >
                <el-option
                  v-for="option in tableBOptions"
                  :key="option.field"
                  :label="option.label"
                  :value="option.field"
                  :disabled="isOptionsDisabled(option, 'B')"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template #default="scope">
              <el-button link type="danger" @click="clickDelete(scope.$index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
  componentPageName: string;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<EditDrawerParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    joinOperatorData.value = cloneDeep(inputData.value);
    joinOperatorData.value.from = cloneDeep(inputData.value.from) || [];
    initData();
    // 判断是否需要重新获取A/B表字段数据
    refreshOptions();
    // 延迟进行表单校验的清空操作才会生效,处理下拉框的change导致进入弹窗的校验触发
    setTimeout(() => {
      clearValidate();
    }, 10);
  },
  {
    immediate: true,
  }
);

function initData() {
  joinOperatorData.value.to = joinOperatorData.value.id;
  joinOperatorData.value.associationTableData =
    cloneDeep(joinOperatorData.value.associationTableData) || [];

  // 为现有数据添加默认运算符（向后兼容）
  joinOperatorData.value.associationTableData?.forEach(item => {
    if (!item.operator) {
      item.operator = "=";
    }
  });
}

// 用于监听菜单是否进行切换到指定页面
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (oldVal === props.componentPageName) {
      flowChartStore.updateTempData(
        inputData.value.nodeType,
        joinOperatorData.value
      );
    }
  }
);

const joinOperatorData = ref<EditDrawerParam>({
  id: "",
  name: "",
});

import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const joinOperatorDataRules = {
  id: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  joinType: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  streamAM: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  streamBM: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  joinCondition: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
};

import { useVueFlow } from "@vue-flow/core";

const { getIncomers } = useVueFlow();

const streamOptions = ref<ComputerOptionsParam[]>([]);

const streamAOptions = ref<ComputerOptionsParam[]>([]);

const streamBOptions = ref<ComputerOptionsParam[]>([]);

function isOptionsStearm(data: NodeOptionsParam, type: "A" | "B") {
  const value =
    type === "A"
      ? joinOperatorData.value.streamBM
      : joinOperatorData.value.streamAM;
  return data.value == value;
}

// A流获取表格字段
const tableAOptions = ref<FieldTableParam[]>([]);

// B流获取表格字段
const tableBOptions = ref<FieldTableParam[]>([]);

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 刷新按钮
function refreshOptions() {
  getlocatedData();
  // 判断是否需要重新获取A/B表字段数据
  if (joinOperatorData.value.streamA && joinOperatorData.value.streamB) {
    handleFieldChange(joinOperatorData.value.streamAM, "A");
    handleFieldChange(joinOperatorData.value.streamBM, "B");
  } else {
    tableAOptions.value.length = 0;
    tableBOptions.value.length = 0;
  }
}

// 获取与之连接数据连接
function getlocatedData() {
  streamOptions.value.length = 0;
  streamAOptions.value.length = 0;
  streamBOptions.value.length = 0;
  const incomers = getIncomers(inputData.value.id);
  let streamBool = false;
  let mapBool = false;
  incomers.forEach((item) => {
    // 判断流数据是否存在
    if (item.data.dataStream && item.data.dataStream == "stream") {
      streamBool = true;
    }
    // 判断批数据是否存在
    if (item.data.dataStream && item.data.dataStream == "map") {
      mapBool = true;
    }
    streamOptions.value.push({
      value: item.id,
      label: item.data.name + "（" + item.id + "）",
      key: item.data.type,
      dataStream: item.data.dataStream,
      nodeType: item.data.nodeType,
    });
  });
  filterStreamOptions(streamBool, mapBool);
  getJoinTypeOptions(streamBool, mapBool);
}

// A/B流连接算子的展示
function filterStreamOptions(streamBool: boolean, mapBool: boolean) {
  streamAOptions.value =
    streamBool && mapBool
      ? streamOptions.value.filter((item) => item.dataStream == "stream")
      : streamOptions.value;
  streamBOptions.value =
    streamBool && mapBool
      ? streamOptions.value.filter((item) => item.dataStream == "map")
      : streamOptions.value;
}

const commonTypeOptions: OptionsParam[] = [
  {
    value: "leftjoin",
    label: "leftjoin",
  },
  {
    value: "innerjoin",
    label: "innerjoin",
  },
  {
    value: "mapjoin",
    label: "mapjoin",
  },
];

const joinTypeOptions = ref<OptionsParam[]>([]);

// 运算符选项定义
const operatorOptions = {
  date: [
    { value: "=", label: "等于" },
    { value: ">", label: "大于" },
    { value: "<", label: "小于" },
    { value: ">=", label: "大于等于" },
    { value: "<=", label: "小于等于" }
  ],
  other: [
    { value: "=", label: "等于" }
  ]
};

// 判断字段是否应该被禁用（date类型字段允许重复选择）
function isOptionsDisabledForDateField(data: FieldTableParam, type: "A" | "B", currentIndex: number) {
  if (type === "B") {
    // B表字段保持原有逻辑
    return isOptionsDisabled(data, type);
  }

  // A表字段：如果是date类型，允许重复选择；其他类型不允许重复选择
  if (data.frontedType === "date") {
    console.log(`Date字段 ${data.field} 允许重复选择`);
    return false; // date类型字段允许重复选择
  }

  // 非date类型字段，检查是否已在其他行中被选择
  const associationData = joinOperatorData.value.associationTableData || [];
  const isDisabled = associationData.some((item, index) => {
    // 排除当前行，检查其他行是否已选择该字段
    return index !== currentIndex && item.selectedAField === data.field;
  });

  console.log(`非Date字段 ${data.field} (类型: ${data.frontedType}) 在行 ${currentIndex} 中${isDisabled ? '被禁用' : '可选择'}`);
  return isDisabled;
}

// 判断字段类型并返回可用的运算符选项
function getAvailableOperators(aField: string, bField: string) {
  if (!aField || !bField) return [];

  const aFieldType = tableAOptions.value.find(
    (key) => key.field === aField
  )?.frontedType;
  const bFieldType = tableBOptions.value.find(
    (key) => key.field === bField
  )?.frontedType;

  console.log('字段类型检查:', { aField, bField, aFieldType, bFieldType });

  // 只有当两个字段类型一致时才返回运算符选项
  const options = aFieldType === "date" ? operatorOptions.date : operatorOptions.other;
  console.log('可用运算符选项:', options);
  return options;
  // if (aFieldType === bFieldType) {
  //   const options = aFieldType === "date" ? operatorOptions.date : operatorOptions.other;
  //   console.log('可用运算符选项:', options);
  //   return options;
  // }

  console.log('字段类型不一致，无可用运算符');
  return [];
}

// 根据A/B流中的连接算子获取模式的枚举
function getJoinTypeOptions(streamBool: boolean, mapBool: boolean) {
  if (streamBool && mapBool) {
    joinTypeOptions.value = commonTypeOptions.filter(
      (item) => item.value === "mapjoin"
    );
  } else if (streamBool || mapBool) {
    joinTypeOptions.value = commonTypeOptions.filter(
      (item) => item.value !== "mapjoin"
    );
  } else {
    joinTypeOptions.value = [];
  }
}

async function handleFieldChange(val: string | undefined, tableType: string) {
  const nodeData = streamOptions.value.find((item) => item.value == val);
  if (!nodeData) return ElMessage.error("该数据源不存在");
  // 调用方法获取字段数据
  const resData = (await getConnectData([nodeData])) as ConnectDataParam;
  const { getTableData, filterData } = resData;
  if (isEmpty(getTableData)) return;
  getTableData.forEach((item: FieldTableParam) => {
    item.label = item.description + "（" + item.field + "）";
  });
  const type = filterData.type;
  const tempType =
    type == "CfgSource"
      ? flowChartStore.inputSource[filterData.id]?.modelLabel
      : type;
  if (tableType == "A") {
    tableAOptions.value = cloneDeep(getTableData);
    joinOperatorData.value.dataStream = filterData?.dataStream;
    joinOperatorData.value.streamA = type == "CfgSource" ? tempType : type;
    // 只有初始进入join连接后才会赋值全部数据
    if (!joinOperatorData.value.tableA) {
      joinOperatorData.value.tableA = cloneDeep(tableAOptions.value);
    }
  } else {
    tableBOptions.value = cloneDeep(getTableData);
    joinOperatorData.value.streamB = type == "CfgSource" ? tempType : type;
    if (!joinOperatorData.value.tableB) {
      joinOperatorData.value.tableB = cloneDeep(tableBOptions.value);
    }
  }
}

// 处理运算符变化
function handleOperatorChange() {
  getTitle();
}

function addAssociation() {
  joinOperatorData.value.associationTableData?.push({
    selectedAField: "", // 单选字段
    selectedBField: "",
    operator: "=", // 默认运算符
  });
}

function clickDelete(index: number) {
  joinOperatorData.value.associationTableData?.splice(index, 1);
}

// 获取此时表格的数据关联数据预览
function getTitle() {
  const data = joinOperatorData.value.associationTableData;
  if (isEmpty(data)) {
    joinOperatorData.value.joinCondition = "";
    return;
  }
  joinOperatorData.value.joinCondition = data
    ?.map((item, index) => {
      if (item.selectedAField && item.selectedBField) {
        const title = index == 0 ? "" : " and ";
        const operator = item.operator || "="; // 使用选择的运算符，默认为 "="
        return `${title}a.${item.selectedAField} ${operator} b.${item.selectedBField}`;
      }
    })
    .join("");
}

const allSelectedAField = ref<(string | number)[]>([]);

const allSelectedBField = ref<(string | number)[]>([]);

// 监听数据变化
watch(
  () => joinOperatorData.value.associationTableData,
  (val) => {
    allSelectedAField.value.length = 0;
    allSelectedBField.value.length = 0;
    val?.forEach((item) => {
      allSelectedAField.value.push(item.selectedAField);
      allSelectedBField.value.push(item.selectedBField);
    });
    getTitle();
  },
  {
    deep: true,
  }
);

// 进行限制处理，已选择的不允许再次选择
function isOptionsDisabled(data: FieldTableParam, type: "A" | "B") {
  const values =
    type === "A" ? allSelectedAField.value : allSelectedBField.value;
  return values.includes(data.field);
}

// 引入通用的校验规则
import { getFormRules, getConnectData } from "@/utils/common";

async function getNewData() {
  // 关联字段规则检查
  if (!fieldCheck()) {
    return false;
  }
  if (joinOperatorData.value.from) {
    joinOperatorData.value.from[0] =
      getFormData(joinOperatorData.value.streamAM) || "";
    joinOperatorData.value.dataStream = streamOptions.value.find(
      (item) => item.value === joinOperatorData.value.streamAM
    )?.dataStream;
    joinOperatorData.value.from[1] =
      getFormData(joinOperatorData.value.streamBM) || "";
  }
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    joinOperatorData.value,
    "基本配置"
  );
  return childRuleBool;
}

function fieldCheck() {
  // 去除表格中的空数据
  const data = joinOperatorData.value.associationTableData?.filter(
    (item) => item.selectedAField && item.selectedBField
  );
  if (!data || data.length === 0) {
    ElMessage.error("请至少添加一行关联字段");
    return false;
  }
  for (let item of data) {
    const aFieldType = tableAOptions.value.find(
      (key) => key.field === item.selectedAField
    )?.frontedType;
    const bFieldType = tableBOptions.value.find(
      (key) => key.field === item.selectedBField
    )?.frontedType;
    if (aFieldType !== bFieldType) {
      ElMessage.error("请确保a表和b表关联字段类型保持一致");
      return false;
    }
    // 检查运算符是否已选择
    if (!item.operator) {
      ElMessage.error("请为每行关联条件选择运算符");
      return false;
    }
    // 为没有运算符的数据设置默认值
    if (!item.operator) {
      item.operator = "=";
    }
  }
  joinOperatorData.value.associationTableData = cloneDeep(data);
  return true;
}

function getFormData(type?: string) {
  if (!type) return;
  return streamOptions.value.find((item) => item.value === type)?.value;
}

// 判断输入源是否修改导致字段进行修改
function checkFieldUpdate() {
  if (!inputData.value.streamAM) return false;
  if (joinOperatorData.value.streamAM !== inputData.value.streamAM) {
    return true;
  }
  if (!inputData.value.streamBM) return false;
  if (joinOperatorData.value.streamBM !== inputData.value.streamBM) {
    return true;
  }
  if (!inputData.value.joinCondition) return false;
  if (joinOperatorData.value.joinCondition !== inputData.value.joinCondition) {
    return true;
  }
  return false;
}

// 关闭弹窗清楚校验
function clearValidate() {
  ruleFormRef.value?.clearValidate();
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
  checkFieldUpdate,
});
</script>

<style lang="scss" scoped>
.page {
  height: calc(100% - 60px);
  padding-right: 8px;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
