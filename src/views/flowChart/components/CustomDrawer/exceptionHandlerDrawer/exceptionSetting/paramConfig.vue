<template>
  <div class="page page-table">
    <el-button class="btn-refresh" @click="refeshField(true)">
      <svg-icon name="reImport" style="margin-right: 8px"></svg-icon>
      重新导入字段
    </el-button>
    <el-form
      class="form-style form-table-style"
      ref="ruleFormRef"
      :model="exceptionHandlerData"
      :rules="exceptionHandlerDataRules"
      :show-message="false"
      label-width="auto"
    >
      <el-form-item prop="abnormalParam" class="form-table-height">
        <el-table
          :data="exceptionHandlerData.abnormalParam"
          class="fullheight"
          scrollbar-always-on
        >
          <el-table-column label="异常参数字段" min-width="150">
            <template #default="scope">
              <el-select
                v-model="scope.row.exceptionField"
                placeholder="请选择"
                disabled
              >
                <el-option
                  v-for="option in abnormalOptions"
                  :key="option.key"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="运算符" width="100" align="center">
            =
          </el-table-column>
          <el-table-column label="异常参数值" min-width="150">
            <template #default="scope">
              <component
                :is="getComponentByType(scope.row.exceptionField)"
                v-model="scope.row.exceptionValue"
                v-bind="getComponentByType(scope.row.exceptionField, 'props')"
              >
                <template v-if="isShowOptions(scope.row.exceptionField)">
                  <el-option
                    v-for="key in getCurOptions(scope.row.exceptionField)"
                    :key="key.label"
                    :label="key.label"
                    :value="key.value"
                  />
                </template>
              </component>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
// 引入通用的校验规则
import { getFormRules } from "@/utils/common";

// 获取父组件的ref
const props = defineProps<{
  showDrawer: boolean;
  stepActive: number;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<EditDrawerParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    exceptionHandlerData.value.id = inputData.value.id;
    exceptionHandlerData.value.abnormalParam = cloneDeep(
      inputData.value.abnormalParam
    );
  },
  {
    immediate: true,
  }
);

const exceptionHandlerData = ref<EditDrawerParam>({
  id: "",
  abnormalParam: [],
});

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 监听步骤发现改变获取最新的数据
watch(
  () => props.stepActive,
  (val) => {
    if (val !== 1) return;
    refeshField();
  }
);

/**
 * @description: 异常参数字段数据刷新
 * @param {*} restore -是否重置
 * @return {*}
 */
function refeshField(restore: boolean = false) {
  const type = flowChartStore.tempComputerData?.businessScene;
  queryFieldTableData(type, restore);
}

const abnormalOptions = ref<NodeOptionsParam[]>([]);

import customApi from "@/api/custom";

/**
 * @description: 获取所有异常参数字段数据
 * @param {*} type -业务类型
 * @param {*} restore -是否重置
 * @return {*}
 */
async function queryFieldTableData(type?: string, restore: boolean = false) {
  if (!type) return;
  try {
    const res = await customApi.getOptionalParameters({ id: type });
    // 查询异常参数字段
    abnormalOptions.value = res.data?.map((item) => {
      const { field, description, frontedType } = item;
      return {
        value: field,
        label: description,
        key: frontedType,
      };
    });
    if (exceptionHandlerData.value.abnormalParam?.length && !restore) return;
    exceptionHandlerData.value.abnormalParam = res.data?.map((item) => {
      const { field, defaultValue } = item;
      return {
        exceptionField: field as string,
        exceptionValue: defaultValue,
      };
    });
  } catch (error) {
    abnormalOptions.value.length = 0;
    console.log(error);
  }
}

// 动态组件的处理
interface TypeOptionsParam {
  value: string | number | boolean;
  label: string;
}

// 布尔值选项
const BOOLEAN_OPTIONS = [
  {
    label: "是",
    value: true,
  },
  {
    label: "否",
    value: false,
  },
];

interface ComponentMapParam {
  key: string;
  options?: TypeOptionsParam[];
}
// 定义类型和组件之间的映射关系
const typeComponentMap: Record<string, ComponentMapParam> = {
  number: {
    key: "inputNumber",
  },
  boolean: {
    key: "select",
    options: BOOLEAN_OPTIONS,
  },
};

// 导入方法模型定义
import { filterValueOptions } from "@/utils/globalField";

/**
 * @description: 获取当前字段类型对应的组件
 * @param {*} type -字段类型
 * @param {*} key - 要查组件的key
 * @return {*}
 */
function getComponentByType(
  type?: string,
  key: "component" | "props" = "component"
) {
  if (!type) return;

  const fieldType = justFieldType(type);
  if (!fieldType) return;

  const curComponent = typeComponentMap[fieldType]?.key;
  if (!curComponent) return;

  return filterValueOptions[curComponent][key];
}

/**
 * @description: 判断是否显示选项options
 * @param {*} field - 字段名
 * @return {*}
 */
function isShowOptions(field: string | number) {
  return justFieldType(field) === "boolean";
}

/**
 * @description: 获取当前字段对应的选项
 * @param {*} type - 字段名
 * @return {*}
 */
function getCurOptions(type?: string) {
  if (!type) return [];
  const fieldType = justFieldType(type);

  if (!fieldType) return [];
  return typeComponentMap[fieldType]?.options ?? [];
}

/**
 * @description: 判断字段类型
 * @param {*} field -字段
 * @return {*}
 */
function justFieldType(field: string | number) {
  return abnormalOptions.value?.find((item) => item.value === field)?.key;
}

import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const exceptionHandlerDataRules = {
  abnormalParam: [
    {
      required: false,
      trigger: ["blur", "change"],
    },
  ],
};

async function getNewData() {
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    exceptionHandlerData.value,
    "输入字段"
  );
  return childRuleBool;
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
});
</script>

<style lang="scss" scoped>
.page {
  height: calc(100% - 80px);
  .form-table-style {
    max-height: calc(100% - 50px);
  }
}
</style>
