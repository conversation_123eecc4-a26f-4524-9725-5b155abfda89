<template>
  <ComStepPage
    ref="comStepRef"
    :stepData="stepData"
    :componentPageName="componentPageName"
    v-bind="$attrs"
  ></ComStepPage>
</template>

<script setup lang="ts">
import ComStepPage from "../../ComStepPage/index.vue";
// 基本属性
import basicConfig from "./exceptionSetting/basicConfig.vue";
// 参数配置
import paramConfig from "./exceptionSetting/paramConfig.vue";

// 定义页面名称
const componentPageName = "exceptionHandlerSetting";

const stepData: StepParam[] = [
  {
    title: "基本属性",
    component: basicConfig,
    index: 0,
  },
  {
    title: "参数配置",
    component: paramConfig,
    index: 1,
  },
];

const comStepRef = ref();

// 判断页面中多个步骤条的校验是否通过
async function getNewData() {
  if (!comStepRef.value) return false;
  const ruleBool = await comStepRef.value.getNewData();
  return ruleBool;
}

// 判断页面中多个步骤条的校验是否通过
async function checkFieldUpdate() {
  if (!comStepRef.value) return false;
  const ruleBool = await comStepRef.value.checkFieldUpdate();
  return ruleBool;
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
  checkFieldUpdate,
});
</script>
