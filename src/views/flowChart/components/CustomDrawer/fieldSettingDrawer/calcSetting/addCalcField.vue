<template>
  <el-dialog
    v-bind="dialogConfig"
    v-model="show"
    @close="resetForm"
    :show-close="false"
    :fullscreen="fullscreen"
    class="dialog-body"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <span :id="titleId" :class="titleClass">{{ dialogConfig.title }}</span>
        <div>
          <img
            v-show="fullscreen"
            src="@/assets/images/not_fullscreen.png"
            @click="fullscreen = false"
            class="notFullScreen"
          />
          <img
            v-show="!fullscreen"
            src="@/assets/images/fullscreen.png"
            @click="fullscreen = true"
            class="fullScreen"
          />
          <el-button @click="show = false" v-show="fullscreen">取消</el-button>
          <el-button type="primary" @click="confirm()" v-show="fullscreen">
            确定
          </el-button>
          <el-icon
            v-show="!fullscreen"
            @click="close"
            :size="20"
            style="margin-left: 10px; cursor: pointer"
          >
            <Close />
          </el-icon>
        </div>
      </div>
    </template>
    <div class="dialog-content">
      <el-form
        ref="ruleFormRef"
        :model="formData"
        label-position="top"
        :rules="formDataRules"
        :require-asterisk-position="'right'"
      >
        <el-form-item
          label="字段名称"
          prop="description"
          class="form-item-style"
          style="width: 673px"
        >
          <el-input
            v-model="formData.description"
            placeholder="请输入字段名称"
            maxlength="20"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="form-item-style"
          label="字段类型"
          prop="frontedType"
          style="width: 215px; margin-left: 24px"
        >
          <el-select v-model="formData.frontedType" >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="字段ID" prop="field">
          <el-input
            v-model="formData.field"
            placeholder="请输入字段名称"
            maxlength="64"
            :disabled="isEditMode"
            :class="{ inputWidth: fullscreen }"
          ></el-input>
        </el-form-item>
      </el-form>
      <div style="margin-bottom: 8px">
        计算公式
        <span style="color: red">*</span>
        <span style="color: #c9cdd4">（请在英文输入法下编辑公式）</span>
      </div>
      <div class="drawer-content">
        <div class="drawer-header">
          <span>公式：</span>
          <div class="fullScreen" @click="copyData">
            <el-icon style="vertical-align: middle"><CopyDocument /></el-icon>
            <span style="vertical-align: middle; margin-left: 4px">复制</span>
          </div>
        </div>
        <div id="drawer-body" v-on:paste="handlePaste">
          <Codemirror
            v-model:value="code"
            :options="cmOptions"
            border
            ref="cmRef"
            height="200"
          ></Codemirror>
          <template v-show="false">
            <Codemirror
              v-model:value="codeTemp"
              :options="cmOptions"
              border
              ref="cmRefTemp"
              height="200"
            ></Codemirror>
          </template>
        </div>
      </div>
      <div class="table-content">
        <span class="content-title">变量名称:</span>
        <el-input
          v-model="inputSearch"
          placeholder="请输入变量名称关键字"
          :suffix-icon="Search"
          :class="{ inputWidth: fullscreen }"
        ></el-input>
        <div class="row-content" :class="{ rowHeight: !fullscreen }">
          <el-row :gutter="24">
            <el-col
              v-for="(item, index) in searchResultData"
              :key="index"
              :span="fullscreen ? 4 : 8"
              @click="selectItem(item)"
            >
              <div class="table-item">
                <el-tooltip
                  class="box-item"
                  :content="item.description"
                  placement="bottom"
                >
                  <span class="item-span">{{ item.description }}</span>
                </el-tooltip>
                <el-tag :class="getTypeColor(item.frontedType)" round>
                  {{ getTypeText(item.frontedType) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="show = false" v-show="!fullscreen">取消</el-button>
      <el-button type="primary" @click="confirm()" v-show="!fullscreen">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { CopyDocument, Close, Search } from "@element-plus/icons-vue";
import "codemirror/mode/javascript/javascript.js";
import Codemirror from "codemirror-editor-vue3";
import type { CmComponentRef } from "codemirror-editor-vue3";
import type {
  Editor,
  EditorConfiguration,
  TextMarker,
  MarkerRange,
} from "codemirror";

// 定义弹窗
const dialogConfig = reactive({
  title: "新增计算字段",
  width: "960px",
  top: "3vh",
  closeOnClickModal: false,
  closeOnPressEscape: false,
});

/**
 *   是否为编辑模式
 */
const isEditMode = computed(() => {
  return dialogConfig.title.includes("编辑");
});

const fullscreen = ref(false);

// 获取父组件的ref
const props = defineProps<{
  inputData: FieldTableParam;
  tableData?: FieldTableParam[];
}>();

// 传递子组件的emit
const emit = defineEmits(["refreshList"]);

// 弹窗的打开和关闭,获取父组件的v-model的值
const show = defineModel<boolean>({ default: false });

const formData = ref<FieldTableParam>({
  field: "",
  description: "",
  fieldType: "",
  frontedType: "",
});

watch(
  () => show.value,
  (val) => {
    if (val) {
      nextTick(() => {
        formData.value = cloneDeep(props.inputData);
        fullscreen.value = false;
        dialogConfig.title = formData.value.field
          ? "编辑计算字段"
          : "新增计算字段";
        formData.value.frontedType = formData.value.frontedType || "number";
        formData.value.field = formData.value.field || "_field_" + Date.now();
        code.value = "";
        if (formData.value.formula) {
          getText(formData.value.formula);
        }
      });
    }
  }
);

function getText(curText: string) {
  if (!cmRef.value) return;
  const cm = cmRef.value.cminstance;
  cm.setValue(curText);
  // 校验规则
  const pattern = /\$(.*?)#/g;
  // 清除原先的mark标记
  cm.getAllMarks().forEach((mark) => mark.clear());
  let newText = cm.getValue();
  let match;

  // 初始化正则表达式的 lastIndex 为 0
  pattern.lastIndex = 0;

  while ((match = pattern.exec(newText)) !== null) {
    const from = cm.posFromIndex(match.index);
    const to = cm.posFromIndex(match.index + match[0].length);
    const replacement = match[1];
    const replaceText = props.tableData?.find(
      (item) => item.field === replacement
    )?.description;
    // 如果替换文本存在则进行替换
    if (replaceText) {
      replaceAndMarkText(cm, from, to, replacement, replaceText);
      // 获取最新的文本
      newText = cm.getValue();
      // 获取最新的正则表达式匹配位置
      pattern.lastIndex =
        newText.indexOf(replaceText, match.index) + replaceText.length;
    }
  }
  code.value = newText;
}

interface Pos {
  line: number;
  ch: number;
  stickey?: boolean;
}

function replaceAndMarkText(
  cm: Editor,
  from: Pos,
  to: Pos,
  replacement: string,
  replaceText: string
) {
  // 如果该字段不存在，则不替换
  if (!replaceText) return;
  // 进行文本替换
  cm.replaceRange(replaceText, from, to);

  // 重新计算标记的结束位置
  const newTo = {
    line: from.line,
    ch: from.ch + replaceText.length,
  };
  // 进行标记
  setMarkSpan(from, newTo, replaceText, replacement);
}

import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const formDataRules = {
  description: [
    {
      required: true,
      message: "请输入字段名称",
      trigger: ["blur", "change"],
    },
  ],
  frontedType: [
    {
      required: true,
      message: "请选择数据类型",
      trigger: ["blur", "change"],
    },
  ],
  field: [
    {
      required: true,
      message: "请输入字段ID",
      trigger: ["blur", "change"],
    },
    {
      pattern: /^[A-Za-z0-9_-]+$/,
      message: "不允许输入特殊字符",
      trigger: ["blur", "change"],
    },
  ],
};

const inputSearch = ref("");

const typeOptions = [
  {
    value: "number",
    label: "数字类型",
  },
  {
    value: "string",
    label: "文本类型",
  }
];

const searchResultData = computed(() => {
  // 一阶段只进行数字类型字段的设置
  const numTable = props.tableData?.filter(
    (item) => item.frontedType === "number"
  );
  if (inputSearch.value) {
    return numTable?.filter((item) => {
      return item.description.includes(inputSearch.value);
    });
  } else {
    return numTable;
  }
});

interface newEditorConfiguration extends EditorConfiguration {
  styleActiveLine: boolean;
}

// 主编辑器
const code = ref();
const cmRef = ref<CmComponentRef>();
const cmOptions: newEditorConfiguration = {
  mode: "javascript",
  theme: "default",
  // 是否行号
  lineNumbers: false,
  readOnly: false,
  // 是否折叠
  lineWrapping: false,
  // 是否高亮当前行
  styleActiveLine: false,
  gutters: [],
};

import { getCopyData } from "@/utils/common";

// 进行文本复制处理
function copyData() {
  let msg = getResultFormula();
  if (!msg) {
    return ElMessage.warning("请输入公式");
  }
  getCopyData(msg);
}

// 监听粘贴事件
function handlePaste(event: ClipboardEvent) {
  var pastedData = event.clipboardData?.getData("text/plain");
  if (pastedData) {
    getText(pastedData);
  }
}

function getTypeColor(type: string) {
  return "tag-text-" + type;
}

function getTypeText(type: string) {
  switch (type) {
    case "number":
      return "数字";
    case "string":
      return "文本";
    case "date":
      return "日期";
    case "enum":
      return "枚举";
    default:
      return type;
  }
}

function selectItem(item: FieldTableParam) {
  insertTextAtCursor(item.description, item.field);
}

const insertTextAtCursor = (text: string, field: string | number) => {
  if (!cmRef.value) return;
  const cm = cmRef.value.cminstance;
  const from = cm.getCursor("from"); // 获取光标当前位置
  const to = cm.getCursor("to"); // 获取光标当前位置
  // 如果有文本被选中，则替换选中的文本，否则在光标位置插入文本
  if (from.line !== to.line || from.ch !== to.ch) {
    // 选中区域不为空，替换选中的文本
    cm.replaceRange(text, from, to);
  } else {
    // 没有文本被选中，插入文本到光标位置
    cm.replaceSelection(text);
  }
  const newCursor = { line: from.line, ch: from.ch + text.length };
  setMarkSpan(from, newCursor, text, field as string);
  cm.setCursor(newCursor);
  cm.focus();
};

function setMarkSpan(from: Pos, to: Pos, text: string, field: string) {
  if (!cmRef.value) return;
  const cm = cmRef.value.cminstance;
  // 创建自定义标签元素
  const spanDom = document.createElement("span");
  spanDom.className = "special-text"; // 自定义样式类
  spanDom.textContent = text; // 设置标签内容

  cm.markText(from, to, {
    replacedWith: spanDom,
    className: "special-text",
    atomic: true,
    title: text,
    attributes: {
      "data-field": field,
    },
  });
}

// 点击确定
async function confirm() {
  // 如果使用async和await获取validate，不会获取到Promise.reject的值，所以需要try catch
  try {
    const valid = await ruleFormRef.value?.validate();
    if (!checkRepeat()) return;
    formData.value.formula = getResultFormula();
    if (!(await checkFieldFormula())) return;
    if (isEditMode.value) {
      ElMessage.success("编辑成功");
    } else {
      ElMessage.success("新增成功");
    }
    show.value = false;
    const data = cloneDeep(formData.value);
    emit("refreshList", data);
  } catch (error) {
    console.log(error);
  }
}

// 检查字段ID是否重复
function checkRepeat() {
  // 编辑状态不检查重复
  if (isEditMode.value) return true;

  const field = formData.value.field;
  if (!field) return false;

  const repeat = !!props.tableData?.some((item) => item.field === field);
  if (repeat) {
    ElMessage.error("字段ID存在重复");
  }
  return !repeat;
}

import customApi from "@/api/custom";

// 检查公式是否合法
async function checkFieldFormula() {
  // if (!formData.value.formula) {
  //   ElMessage.warning("请输入公式");
  //   return false;
  // }

  const tableArray = cloneDeep(props.tableData);
  tableArray?.push(formData.value);
  const param = {
    tableArray: tableArray,
  };
  try {
    const res = await customApi.checkFieldFormula(param);
    if (formData.value.frontedType === "string") {
      formData.value.fieldType = "String";
    }
    if (formData.value.frontedType === "number") {
      formData.value.fieldType = "double";
    }

    return true;
  } catch (error) {
    console.log(error);
    return false;
  }
}

const codeTemp = ref();
const cmRefTemp = ref<CmComponentRef>();

function getResultFormula() {
  if (!cmRef.value) return;
  const cm = cmRef.value.cminstance;
  let value = cm.getValue();
  // 去除文本中的$-#之间的字符
  value = value.replace(/\$[^#]*#/g, "");
  // 获取所有的标记并从后到前排序替换
  const allMarks = cm.getAllMarks();
  allMarks.sort((a: TextMarker, b: TextMarker) => {
    const aRange = a.find() as MarkerRange;
    const bRange = b.find() as MarkerRange;
    if (aRange.from.line === bRange.from.line) {
      return bRange.from.ch - aRange.from.ch;
    }
    return bRange.from.line - aRange.from.line;
  });
  return getMarksResult(value, allMarks);
}

function getMarksResult(value: string, allMarks: TextMarker[]) {
  // 将主编辑中的文本复制到临时编辑器中并在临时编辑器中进行数据的更改
  if (!cmRefTemp.value) return;
  const cmTemp = cmRefTemp.value.cminstance;
  codeTemp.value = value;
  cmTemp.setValue(value);
  allMarks.forEach((mark) => {
    if (mark.className === "special-text" && mark.attributes) {
      const markedSpans = mark.find() as MarkerRange;
      if (!markedSpans) return;
      const replacement = "$" + mark.attributes["data-field"] + "#";
      cmTemp.replaceRange(replacement, markedSpans.from, markedSpans.to);
    }
  });
  return cmTemp.getValue();
}

// 用于关闭弹窗后重置表单操作
function resetForm() {
  ruleFormRef.value?.resetFields();
}

onMounted(() => {
  setTimeout(() => {
    cmRef.value?.refresh();
  }, 1000);

  setTimeout(() => {
    cmRef.value?.cminstance.isClean();
  }, 3000);
});

onUnmounted(() => {
  cmRef.value?.destroy();
});
</script>
<style lang="scss" scoped>
.dialog-body {
  .my-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .notFullScreen {
      cursor: pointer;
      margin-right: 8px;
      vertical-align: middle;
    }
  }
  .fullScreen {
    cursor: pointer;
  }
  .dialog-content {
    border-top: 1px solid #e5e6eb;
    border-bottom: 1px solid #e5e6eb;
    padding: 24px 8px;
    height: calc(100% - 32px);
    min-height: 700px;
    .table-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 4px 0px;
      padding: 0px 16px;
      height: 32px;
      line-height: 32px;
      cursor: pointer;
      border-radius: 4px 4px 4px 4px;
      .item-span {
        font-weight: 400;
        font-size: 14px;
        color: #1d2129;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .table-item:hover {
      background: #f2f3f5;
    }
    .form-item-style {
      display: inline-block;
      vertical-align: middle;
    }
    :deep(.el-form-item) {
      margin-bottom: 24px;
    }
    .drawer-content {
      border: 1px solid #e5e6eb;
      width: 100%;
      .drawer-header {
        background: #f2f3f5;
        padding: 0px 24px;
        line-height: 32px;
        height: 32px;
        display: flex;
        justify-content: space-between;
      }
    }
    .table-content {
      margin-top: 24px;
      height: calc(100% - 420px);
      .content-title {
        display: block;
        margin-bottom: 4px;
      }
      .row-content {
        width: 100%;
        margin-top: 16px;
        height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;
      }
      .rowHeight {
        max-height: 260px;
      }
    }
    .inputWidth {
      width: 673px;
    }
    .tag-text-number {
      color: rgba(76, 166, 255, 1);
      background-color: rgba(76, 166, 255, 0.1);
    }
  }
}
</style>
<style lang="scss">
// 编辑器的样式处理
.dialog-body {
  .el-dialog__body {
    height: calc(100% - 80px) !important;
  }
}
.codemirror-container {
  border: none !important;
  letter-spacing: 1px;
}
.CodeMirror-gutters {
  display: none;
}
.CodeMirror pre.CodeMirror-line {
  padding: 0 10px;
}
.special-text {
  background: #eaf2fd;
  color: #2f7deb;
  border-radius: 2px;
  display: inline-block;
  font-size: 12px;
  margin: 1px;
  padding: 0 5px;
}
</style>
