<template>
  <el-dialog v-bind="dialogConfig" v-model="show" @close="resetForm">
    <el-form
      ref="ruleFormRef"
      :model="formData"
      label-position="top"
      :require-asterisk-position="'right'"
      :rules="formDataRules"
    >
      <el-form-item label="字段名称" prop="description">
        <el-input v-model="formData.description" maxlength="20"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="confirm()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
// 定义弹窗
const dialogConfig = {
  title: "重命名",
  width: "480px",
  closeOnClickModal: false,
  closeOnPressEscape: false,
};

const props = defineProps<{
  inputData?: FieldTableParam;
}>();

// 传递子组件的emit
const emit = defineEmits(["refreshList"]);

// 弹窗的打开和关闭,获取父组件的v-model的值
const show = defineModel<boolean>({ default: false });

const formData = ref<FieldTableParam>({
  field: "",
  description: "",
  fieldType: "",
  frontedType: "",
});

watch(
  () => show.value,
  (val) => {
    if (props.inputData) {
      formData.value = cloneDeep(props.inputData);
    }
  }
);

import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const formDataRules = {
  description: [
    {
      required: true,
      message: "请输入字段名称",
      trigger: ["blur", "change"],
    },
  ],
};

// 点击确定
async function confirm() {
  // 如果使用async和await获取validate，不会获取到Promise.reject的值，所以需要try catch
  try {
    const valid = await ruleFormRef.value?.validate();
    ElMessage.success("重命名成功");
    show.value = false;
    emit("refreshList", formData.value);
  } catch (error) {
    console.log(error);
  }
}

// 用于关闭弹窗后重置表单操作
function resetForm() {
  ruleFormRef.value?.resetFields();
}
</script>
<style lang="scss" scoped></style>
