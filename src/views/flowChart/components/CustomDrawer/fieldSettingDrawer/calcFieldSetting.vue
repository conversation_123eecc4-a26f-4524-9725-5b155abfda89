<template>
  <div class="page page-table">
    <el-form
      class="form-style form-table-style"
      ref="ruleFormRef"
      label-position="top"
      require-asterisk-position="right"
      label-width="auto"
      :model="calcOperatorData"
      :rules="calcOperatorDataRules"
      :show-message="false"
    >
      <el-form-item label="节点名称" prop="name">
        <el-input v-model="calcOperatorData.name" maxlength="20"></el-input>
        <div class="form-item-tips">指定任何有意义且能描述本节点的说明</div>
      </el-form-item>
      <el-form-item label="节点ID" prop="id">
        <el-input v-model="calcOperatorData.id" disabled></el-input>
        <div class="form-item-tips">节点ID不能重复</div>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="calcOperatorData.remark"
          type="textarea"
          :rows="1"
        ></el-input>
      </el-form-item>
      <el-button @click="addCalcfield" :icon="CirclePlus">
        新增计算字段
      </el-button>
      <div class="form-item-tips" style="margin: 16px 0 8px 0">
        上游算子默认全部勾选展示，取消勾选可以隐藏该字段；新增的计算字段默认全部展示，不可隐藏
      </div>
      <el-table
        :data="tableData"
        border
        ref="tableRef"
        row-key="field"
        scrollbar-always-on
        @selection-change="handleSelectionChange"
        style="height: calc(100% - 366px); min-height: 120px"
      >
        <el-table-column width="55" label="排序" align="center">
          <template #default="scope">
            <el-image
              :src="isHover[scope.$index] ? tableUrl[0] : tableUrl[1]"
              alt="图片"
              class="drag-handler"
              @mouseenter="isHover[scope.$index] = true"
              @mouseleave="isHover[scope.$index] = false"
            />
          </template>
        </el-table-column>
        <el-table-column type="selection" :selectable="selectable" width="55" />
        <template v-for="item in fieldTableHeader">
          <el-table-column
            v-bind="item"
            show-overflow-tooltip
          ></el-table-column>
        </template>
        <el-table-column label="操作" width="110" align="center">
          <template #default="scope">
            <el-button
              v-show="selectable(scope.row)"
              link
              type="primary"
              @click="renameClick(scope.row)"
            >
              重命名
            </el-button>
            <el-button
              v-show="!selectable(scope.row)"
              link
              type="primary"
              @click="handleClick(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-show="!selectable(scope.row)"
              link
              type="danger"
              @click="deleteClick(scope.$index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <renameField
      v-model="showRename"
      :inputData="rowData"
      @refreshList="refreshList"
    ></renameField>
    <addCalcField
      v-model="showCalcField"
      :inputData="rowData"
      :tableData="tableData"
      @refreshList="refreshList"
    ></addCalcField>
  </div>
</template>

<script setup lang="ts">
import { CirclePlus } from "@element-plus/icons-vue";

// 引入通用的校验规则
import {
  getFormRules,
  getConnectData,
  getNodeFrom,
  getResetField,
} from "@/utils/common";

import addCalcField from "./calcSetting/addCalcField.vue";

import renameField from "./calcSetting/renameField.vue";

const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<EditDrawerParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    calcOperatorData.value = cloneDeep(inputData.value);
    calcOperatorData.value.from = cloneDeep(inputData.value.from) || [];
    calcOperatorData.value.to = inputData.value.id;
    refreshOptions();
  },
  {
    immediate: true,
  }
);

const calcOperatorData = ref<EditDrawerParam>({
  id: "",
  name: "",
});

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 定义页面名称
const componentPageName = "calcFieldSetting";

// 用于监听菜单是否进行切换到指定页面
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (oldVal === componentPageName) {
      setCalcOperatorData();
      flowChartStore.updateTempData(
        inputData.value.nodeType,
        calcOperatorData.value
      );
    }
  }
);

// 表单校验规则
import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const calcOperatorDataRules = {
  id: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
};

import { fieldTableHeader } from "@/utils/globalField";

const tableData = ref<FieldTableParam[]>([]);

import { useVueFlow } from "@vue-flow/core";

const { getIncomers } = useVueFlow();

// 数据所在节点
const locatedNodeOptions = ref<ComputerOptionsParam[]>([]);

// 回显判断是否重新获取字段信息
async function refreshOptions() {
  getlocatedData();
  if (isEmpty(calcOperatorData.value.allfieldTable)) {
    await loadField();
    getCheckedData();
  } else {
    tableData.value = cloneDeep(calcOperatorData.value.allfieldTable) || [];
    // 使用nextTick进行数据回显,防止数据未渲染完成
    nextTick(() => {
      selectedField.value = cloneDeep(calcOperatorData.value.fieldTable) || [];
      getCheckedData();
    });
  }
}

// 获取与之连接数据连接
function getlocatedData() {
  locatedNodeOptions.value.length = 0;
  // 获取当前节点的所有入边
  const incomers = getIncomers(inputData.value.id);
  locatedNodeOptions.value = getResetField(incomers);
}

const selectedField = ref<FieldTableParam[]>([]);

async function loadField() {
  tableData.value.length = 0;
  // 调用方法获取字段数据
  const resData = (await getConnectData(
    locatedNodeOptions.value
  )) as ConnectDataParam;
  const { getTableData, filterData } = resData;
  calcOperatorData.value.dataStream = filterData?.dataStream;
  if (isEmpty(getTableData)) return;
  tableData.value = cloneDeep(getTableData);
  selectedField.value = cloneDeep(getTableData);
}

function handleSelectionChange(val: FieldTableParam[]) {
  selectedField.value = cloneDeep(val);
}

// 图片切换
const tableUrl = [
  new URL("../../../../../assets/images/sort_white.png", import.meta.url).href,
  new URL("../../../../../assets/images/sort_black.png", import.meta.url).href,
];

// 定义图片悬浮状态切换
const isHover = ref<boolean[]>([]);

const selectable = (row: FieldTableParam) => {
  // 只要是计算字段就不可以选择
  return !row.formula;
};

const tableRef = ref();

// 已勾选的数据进行回显
function getCheckedData() {
  const selected = selectedField.value?.map((item) => {
    return item.field;
  });
  tableData.value.forEach((item) => {
    tableRef.value!.toggleRowSelection(item, selected?.includes(item.field));
  });
}

// 新增计算字段
function addCalcfield() {
  showCalcField.value = true;
  rowData.value = {
    field: "",
    description: "",
    fieldType: "",
    frontedType: "",
  };
}

const showRename = ref(false);

const rowData = ref<FieldTableParam>({
  field: "",
  description: "",
  fieldType: "",
  frontedType: "",
});

// 重命名
function renameClick(row: FieldTableParam) {
  showRename.value = true;
  rowData.value = row;
}

// 刷新列表
function refreshList(data: FieldTableParam) {
  addOrUpdate(tableData.value, data, "table");
  addOrUpdate(selectedField.value, data);
  // 修改之后需要重新勾选上
  getCheckedData();
}

// 判断是更新还是新增
function addOrUpdate(
  array: FieldTableParam[],
  data: FieldTableParam,
  type?: string
) {
  const index = array.findIndex((item) => item.field === data.field);
  if (index === -1) {
    array.unshift(data);
  } else {
    if (type === "table") {
      // 先去除原先的数据残留
      tableRef.value!.toggleRowSelection(array[index], false);
    }
    array.splice(index, 1, data);
  }
}

const showCalcField = ref(false);

// 编辑
function handleClick(row: FieldTableParam) {
  showCalcField.value = true;
  rowData.value = row;
}

// 删除二次确定
function deleteClick(index: number) {
  ElMessageBox.confirm("此操作将永久删除该字段, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      tableData.value.splice(index, 1);
      ElMessage.success("删除成功");
    })
    .catch(() => {
      ElMessage.warning("已取消删除");
    });
}

// 传递子组件的emit
const emit = defineEmits(["updateNodeInfo", "closeDrawer"]);

// 进行保存校验
async function getNewData() {
  setCalcOperatorData();
  if (calcOperatorData.value.fieldTable?.length === 0) {
    ElMessage.error("请至少选择一个字段");
    return false;
  }
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    calcOperatorData.value,
    "基本配置"
  );
  return childRuleBool;
}

function setCalcOperatorData() {
  calcOperatorData.value.allfieldTable = cloneDeep(tableData.value);
  calcOperatorData.value.fieldTable = sortData();
  calcOperatorData.value.filterTable = sortData();
  calcOperatorData.value.from = getNodeFrom(locatedNodeOptions.value);
}

// 勾选的数据要和表格内排序保持一致
function sortData() {
  const arr: FieldTableParam[] = [];
  tableData.value.forEach((item) => {
    const curData = selectedField.value.find(
      (tableItem) => tableItem.field === item.field
    );
    if (curData?.field) {
      arr.push(curData);
    }
  });
  return arr;
}

// 判断输入源是否修改导致字段进行修改
function checkFieldUpdate() {
  if (isEmpty(inputData.value.fieldTable)) return false;
  return !isEqual(
    calcOperatorData.value.fieldTable,
    inputData.value.fieldTable
  );
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
  checkFieldUpdate,
});

import Sortable from "sortablejs";

onMounted(() => {
  // 拖拽排序
  new Sortable(
    tableRef.value.$el.querySelector(".el-table__body-wrapper tbody"),
    {
      animation: 150,
      preventOnFilter: false,
      handle: ".drag-handler",
      onEnd: (evt) => {
        evt.stopPropagation();
        const { oldIndex, newIndex } = evt;
        if (oldIndex === newIndex) return;
        const movedItem = tableData.value.splice(evt.oldIndex!, 1)[0];
        tableData.value.splice(evt.newIndex!, 0, movedItem);
        // 拖动结束后清除所有的悬浮效果
        isHover.value.length = 0;
      },
    }
  );
});
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
  .drag-handler {
    cursor: pointer;
    height: 20px;
    vertical-align: middle;
  }
}
</style>
