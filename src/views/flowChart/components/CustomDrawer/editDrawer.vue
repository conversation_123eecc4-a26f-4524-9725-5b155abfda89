<template>
  <ComMenuDrawer
    v-model="showDrawer"
    :leftMenuList="leftMenuList"
    :selectMenuItem="keyMenuItem"
    :size="isLongWidth ? 960 : 800"
    :showConfirm="showConfirm"
    @updateSelect="updateSelect"
    @updateNodeInfo="updateDrawer"
    @previousStep="previousStep"
    @nextStep="nextStep"
  >
    <template v-for="item in leftComponentList">
      <component
        :is="item.component"
        v-show="keyMenuItem === item.name"
        ref="childrenRef"
        :showDrawer="showDrawer"
        :keyMenuItem="keyMenuItem"
        @closeDrawer="showDrawer = false"
        @updateNodeInfo="updateDrawer"
      ></component>
    </template>
  </ComMenuDrawer>
</template>

<script setup lang="ts">
import ComMenuDrawer from "@/components/ComMenuDrawer/index.vue";

// 编辑框弹窗大于正常宽度设置为960px
const DRAWER_WIDTH = ["StreamJoinFunction", "GeneralFilterFunction"];

const isLongWidth = computed(() => {
  if (!props.inputData.type) return false;
  return DRAWER_WIDTH.includes(props.inputData.type);
});

import { drawerMenuList } from "@/utils/globalField";

// 获取父组件的ref
const props = defineProps<{
  inputData: EditDrawerParam;
  showConfirm?: boolean;
}>();

// 传递子组件的emit
const emit = defineEmits(["updateNodeInfo"]);

const showDrawer = defineModel<boolean>({ default: false });

// 当前计算算子的侧边栏左侧菜单
const leftMenuList = computed(() => {
  const filterList = drawerMenuList.filter(
    (item) => item.type === props.inputData.type
  );
  return filterList.length > 0 ? filterList[0].subMenu : [];
});

// 当前菜单中所有的组件
const leftComponentList = computed(() => {
  return getAllComponent(leftMenuList.value);
});

// 当前选中的菜单项
const currentMenuItem = computed(() => {
  const keyItem = leftMenuList.value;
  if (!keyItem || keyItem.length == 0) return "";
  if (keyItem[0].key) return keyItem[0].key;
  if (keyItem[0].children && keyItem[0].children.length > 0)
    return keyItem[0].children[0].key;
  return "";
});

const keyMenuItem = ref<string>("");

watch(
  () => showDrawer.value,
  (val) => {
    if (val) {
      nextTick(() => {
        keyMenuItem.value = currentMenuItem.value || "";
      });
    } else {
      clearNodeInfo(val);
    }
  }
);

function updateSelect(key: string) {
  keyMenuItem.value = key;
}

interface ComponentListParam {
  name?: string;
  component: any;
}

function getAllComponent(data?: MenuItemParam[]) {
  if (!data || isEmpty(data)) return [];
  const componentList: ComponentListParam[] = [];
  for (const item of data) {
    if (item.component) {
      componentList.push({
        name: item.key,
        component: item.component,
      });
    }
    if (item.children && item.children.length > 0) {
      componentList.push(...getAllComponent(item.children));
    }
  }
  return componentList;
}

async function updateDrawer() {
  const ruleBool = await getDrawerData();
  if (!ruleBool) return;
  let newData;
  switch (props.inputData.nodeType) {
    case "source":
      newData = flowChartStore.tempInputData;
      break;
    case "sink":
      newData = flowChartStore.tempOutputData;
      break;
    default:
      newData = flowChartStore.tempComputerData;
      break;
  }
  if (!newData) return;
  flowChartStore.updateData(props.inputData.nodeType, newData);
  isFieldChange();
  emit("updateNodeInfo", newData);
  showDrawer.value = false;
}

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 用于弹窗关闭后清空临时数据
function clearNodeInfo(bool: boolean) {
  if (bool) return;
  keyMenuItem.value = "";
  nextTick(() => {
    // 清空二次临时数据
    flowChartStore.clearTempData();
  });
}

const childrenRef = ref<ChildrenComponentParam[]>([]);

// 引入通用的校验规则
import {
  getFormRulesAll,
  checkAllFieldUpdate,
  clearChildrenNode,
} from "@/utils/common";

// 用于判断是否所有表单都填写正确
async function getDrawerData() {
  const ruleBool = await getFormRulesAll(childrenRef);
  return ruleBool;
}

import { useVueFlow } from "@vue-flow/core";

const { toObject } = useVueFlow();

// 用于判断编辑情况下字段是否进行修改
async function isFieldChange() {
  const ruleBool = await checkAllFieldUpdate(childrenRef);
  if (ruleBool) {
    clearChildrenNode(
      props.inputData.id,
      toObject().nodes,
      toObject().edges,
      "fieldChange"
    );
  }
}

// 步骤条的当前激活的步骤 --- 步骤条专用
interface StepModel {
  previousTime: number; // 上一步的时间
  nextTime: number; // 下一步的时间
}

const stepModel = ref<StepModel>({
  previousTime: 0,
  nextTime: 0,
});

function previousStep() {
  stepModel.value.previousTime = new Date().getTime();
}

function nextStep() {
  stepModel.value.nextTime = new Date().getTime();
}

// 传递到孙组件使用
provide("stepModel", stepModel);
</script>

<style lang="scss" scoped></style>
