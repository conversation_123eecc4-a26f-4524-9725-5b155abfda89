<template>
  <div>
    <t-tag-input
      v-model="tags"
      v-bind="$attrs"
      :inputValue="inputValue"
      :onInputChange="inputChange"
    >
      <template #collapsedItems="{ value: v, onClose }">
        <t-popup v-if="count > 0">
          <template #content>
            <t-tag
              v-for="(item, index) in collapsedTags"
              :key="item"
              style="margin-right: 4px"
              closable
              @close="onClose({ e: $event, index: minCollapsedNum + index })"
            >
              {{ item }}
            </t-tag>
          </template>
          <t-tag>+{{ count }}</t-tag>
        </t-popup>
      </template>
    </t-tag-input>
  </div>
</template>

<script setup lang="ts">
import {
  TagInput as TTagInput,
  Popup as TPopup,
  Tag as TTag,
} from "tdesign-vue-next";

import { InputValueChangeContext } from "tdesign-vue-next";

// 获取父组件的ref
const props = withDefaults(
  defineProps<{
    inputType?: string;
  }>(),
  {
    inputType: "string",
  }
);

const minCollapsedNum = ref(3);

// 获取父组件v-model传递的值
const tags = defineModel<(number | string)[]>({ default: [] });

const count = computed(() => {
  return tags.value.length - minCollapsedNum.value;
});

const collapsedTags = computed(() => {
  return tags.value.slice(minCollapsedNum.value);
});

const inputValue = ref("");

// 引入通用的校验规则
import { common } from "@/utils/common";

function inputChange(val: string, context?: InputValueChangeContext) {
  // 失去焦点也要添加tag标签
  if (context?.trigger === "blur" && inputValue.value) {
    tags.value.push(inputValue.value);
  }
  if (props.inputType === "number") {
    inputValue.value = common.handleNum(val, 6);
  } else {
    inputValue.value = val;
  }
}
</script>
