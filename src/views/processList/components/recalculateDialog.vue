<template>
  <el-dialog
    v-model="dialogVisible"
    title="重算设置"
    width="400px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="top"
    >
      <el-form-item label="开始时间" prop="startTime" required>
        <el-date-picker
          v-model="form.startTime"
          type="date"
          placeholder="请选择开始时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="form.endTime"
          type="date"
          placeholder="请选择结束时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
    <!-- 新增流程变量输入表格 -->
    <el-table :data="variables" style="width: 100%; margin-bottom: 16px;">
      <el-table-column label="变量名称" prop="name">
        <template #default="scope">
          <el-input v-model="scope.row.name" placeholder="请输入变量名称" />
        </template>
      </el-table-column>
      <el-table-column label="变量标识" prop="label">
        <template #default="scope">
          <el-input v-model="scope.row.label" placeholder="请输入变量标识" />
        </template>
      </el-table-column>
      <el-table-column label="值" prop="value">
        <template #default="scope">
          <el-input v-model="scope.row.value" placeholder="请输入值" />
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="danger" size="small" @click="removeVariable(scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-button type="primary" @click="addVariable">新增变量</el-button>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          启动
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import moment from 'moment'
import { cloneDeep } from 'lodash-es'

interface RecalculateForm {
  startTime: string
  endTime: string
}

interface Props {
  visible: boolean
  rowData?: ProcessTableParam
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  rowData: undefined
})

const emit = defineEmits<{
  (evt: 'update:visible', value: boolean): void
  (evt: 'confirm', data: RecalculateForm & { id: string | number; jobId: string; jobName: string; variables: VariableParam[] }): void
}>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const form = ref<RecalculateForm>({
  startTime: '',
  endTime: ''
})

// 表单验证规则
const rules: FormRules<RecalculateForm> = {
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ]
}

// 初始化表单数据
const initForm = () => {
  const now = moment()
  const today = now.format('YYYY-MM-DD')

  // 检查是否有历史重算参数进行回显
  const hasLastRecalculateData = props.rowData?.lastRecalculateStartTime || props.rowData?.lastRecalculateVariables?.length

  form.value = {
    startTime: hasLastRecalculateData ? (props.rowData?.lastRecalculateStartTime || '') : '',
    endTime: today
  }

  // 回显变量数据
  if (hasLastRecalculateData && props.rowData?.lastRecalculateVariables?.length) {
    variables.value = cloneDeep(props.rowData.lastRecalculateVariables)
  } else {
    variables.value = []
  }
}

// 监听弹窗显示状态，初始化表单
watch(() => props.visible, (visible) => {
  if (visible) {
    initForm()
  }
})

// 关闭弹窗
const handleClose = () => {
  formRef.value?.resetFields()
  variables.value = []
  emit('update:visible', false)
}

// 确认提交
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (!props.rowData) {
      ElMessage.error('缺少流程数据')
      return
    }
    
    loading.value = true
    
    // 验证时间逻辑
    if (form.value.startTime && form.value.endTime) {
      const startTime = moment(form.value.startTime, 'YYYY-MM-DD')
      const endTime = moment(form.value.endTime, 'YYYY-MM-DD')
      const today = moment().startOf('day')

      if (startTime.isAfter(endTime)) {
        ElMessage.error('开始时间不能晚于结束时间')
        loading.value = false
        return
      }
      if (endTime.isAfter(today)) {
        ElMessage.error('结束时间不能晚于当前日期')
        loading.value = false
        return
      }
    }
    
    // 提交数据，合并变量
    emit('confirm', {
      ...form.value,
      id: props.rowData.id,
      jobId: props.rowData.jobId || '',
      jobName: props.rowData.jobName,
      variables: variables.value.filter(v => v.name || v.label || v.value)
    })
    emit('update:visible', false)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 新增变量
const variables = ref<VariableParam[]>([])

const addVariable = () => {
  variables.value.push({ name: '', label: '', value: '' })
}

const removeVariable = (index: number) => {
  variables.value.splice(index, 1)
}
</script>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
