<template>
  <div class="card-container">
    <template v-for="(item, index) in tempTableData" :key="index">
      <el-card
        shadow="hover"
        class="card-style"
        :class="{ highLight: isActive[item.jobName] }"
        @click.stop="lightClick(item, $event)"
      >
        <div class="card-header">
          <div style="width: calc(100% - 60px)">
            <svg-icon
              name="card"
              style="width: 20px; height: 20px"
              class="text-middle"
            ></svg-icon>
            <el-tooltip :content="item.alias" placement="bottom">
              <span class="header-title text-middle text-ellipsis">
                {{ item.alias }}
              </span>
            </el-tooltip>
          </div>
          <div>
            <el-icon
              size="20"
              class="text-middle"
              @click.stop="handleClick(item)"
              :class="
                disabledDesignClick(item) ? 'text-disabled' : 'text-pointer'
              "
            >
              <EditPen />
            </el-icon>
            <el-icon
              size="20"
              style="margin-left: 12px; color: #ff7d00"
              class="text-middle"
              @click.stop="recalculateClick(item)"
              :class="
                disabledDesignClick(item) ? 'text-disabled' : 'text-pointer'
              "
            >
              <Refresh />
            </el-icon>
            <el-icon
              size="20"
              style="margin-left: 12px; color: #f76560"
              class="text-middle"
              @click.stop="deleteClick(item)"
              :class="
                disabledDesignClick(item) ? 'text-disabled' : 'text-pointer'
              "
            >
              <Delete />
            </el-icon>
          </div>
        </div>
        <div class="card-content">
          <el-descriptions :column="1">
            <template
              v-for="(header, headerIndex) in processTableHeader"
              :key="headerIndex"
            >
              <el-descriptions-item
                :label="header.label"
                v-if="header.prop == 'status'"
              >
                <el-tag :class="getStatusColor(item[header.prop])" round>
                  {{ item[header.prop] }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item
                :label="header.label"
                v-else-if="header.prop == 'jobName'"
              >
                <el-tooltip
                  :content="
                    header.format
                      ? header.format(item[header.prop])
                      : item[header.prop]
                  "
                  placement="bottom"
                >
                  <span class="text-ellipsis content-title">
                    {{
                      header.format
                        ? header.format(item[header.prop])
                        : item[header.prop]
                    }}
                  </span>
                </el-tooltip>
              </el-descriptions-item>
              <el-descriptions-item :label="header.label" v-else>
                {{
                  header.format
                    ? header.format(item[header.prop])
                    : item[header.prop]
                }}
              </el-descriptions-item>
            </template>
          </el-descriptions>
        </div>
        <el-divider style="margin: 0; margin-bottom: 16px" />
        <div class="card-footer">
          <el-switch
            size="large"
            v-model="item.isRunning"
            inline-prompt
            :active-text="isSubmittingStatus(item) ? '提交' : '运行'"
            :inactive-text="isSubmittingStatus(item) ? '关闭' : '停止'"
            :style="{
              '--el-switch-on-color': isSubmittingStatus(item)
                ? 'var(--el-color-primary)'
                : '',
              '--el-switch-off-color': isSubmittingStatus(item)
                ? 'var(--el-color-primary)'
                : '',
            }"
            style="height: 24px; margin-right: 12px"
            :loading="isSubmittingStatus(item)"
            :before-change="() => beforeChange(item)"
            @change="switchChange(item)"
          ></el-switch>
          <el-switch
            size="large"
            v-model="item.isRecalculating"
            inline-prompt
            :active-text="isRecalculatingSubmittingStatus(item) ? '提交' : '重算'"
            :inactive-text="isRecalculatingSubmittingStatus(item) ? '关闭' : '停止'"
            :style="{
              '--el-switch-on-color': isRecalculatingSubmittingStatus(item)
                ? 'var(--el-color-primary)'
                : '',
              '--el-switch-off-color': isRecalculatingSubmittingStatus(item)
                ? 'var(--el-color-primary)'
                : '',
            }"
            style="height: 24px"
            :loading="isRecalculatingSubmittingStatus(item)"
            :before-change="beforeRecalculateChange.bind(null, item)"
            @change="recalculateSwitchChange(item)"
          ></el-switch>
          <div>
            <el-button
              link
              type="primary"
              @click.stop="routeClick(item, 'view')"
            >
              查看流程
            </el-button>
            <el-button
              link
              type="primary"
              @click.stop="routeClick(item, 'design')"
              :disabled="disabledDesignClick(item)"
            >
              流程设计
            </el-button>
          </div>
        </div>
      </el-card>
    </template>
  </div>
</template>

<script setup lang="ts">
import { EditPen, Delete, Refresh } from "@element-plus/icons-vue";

const props = defineProps<{
  tableData: ProcessTableParam[];
  watchAllSelect: boolean;
  selectedValue: ProcessTableParam[];
  isIndeterminate: boolean;
}>();

const emit = defineEmits([
  "routeClick",
  "handleClick",
  "deleteClick",
  "recalculateClick",
  "cancelRecalculateTask",
  "handleSelectionChange",
  "refreshList",
  "runFlinkTask",
  "cancelFlinkTask",
]);

import { common } from "@/utils/common";

// 定义流程列表表表头信息
const processTableHeader = [
  {
    prop: "jobName",
    label: "作业名称",
    format: common.formatTextContent,
  },
  {
    prop: "updateTime",
    label: "最后修改时间",
    format: common.formatTime,
  },
  {
    prop: "status",
    label: "流程状态",
  },
];

// 路由跳转
function routeClick(row: CardMapParam, type: string) {
  emit("routeClick", row, type);
}

// 流程设计按钮的置灰处理
const DISABLE_STATUS_DATA = ["RUNNING", "CANCELLING", "SUBMITTED"];

function disabledDesignClick(row: CardMapParam) {
  if (!row.status) return false;
  return DISABLE_STATUS_DATA.includes(row.status);
}

const DISABLED_DATA = ["CANCELLING", "SUBMITTED"];

function disabledClick(row: CardMapParam) {
  if (!row.status) return false;
  return DISABLED_DATA.includes(row.status);
}

// 判断是否显示"提交"状态（RECALCULATING状态应显示"运行"而不是"提交"）
function isSubmittingStatus(row: CardMapParam) {
  if (!row.status) return false;
  const SUBMITTING_STATUS = ["CANCELLING", "SUBMITTED"];
  return SUBMITTING_STATUS.includes(row.status);
}

// 判断重算是否显示"提交"状态
function isRecalculatingSubmittingStatus(row: CardMapParam) {
  // 当重算状态为RECALCULATING时，显示"提交"状态（loading状态）
  return row.recalculateStatus === 'RECALCULATING' || row.isRecalculating;
}

// 编辑
function handleClick(row: CardMapParam) {
  if (disabledDesignClick(row)) return;
  emit("handleClick", row);
}

// 删除
function deleteClick(row: CardMapParam) {
  if (disabledDesignClick(row)) return;
  emit("deleteClick", row);
}

// 重算
function recalculateClick(row: CardMapParam) {
  if (disabledDesignClick(row)) return;
  emit("recalculateClick", row);
}

// 重算开关变化前的处理
function beforeRecalculateChange(row: CardMapParam): Promise<boolean> {
  return new Promise<boolean>((resolve) => {
    if (!row.isRecalculating) {
      // 要开启重算，显示重算弹窗
      emit("recalculateClick", row);
      resolve(false); // 阻止开关自动切换，由弹窗确认后手动设置
    } else {
      // 要关闭重算，直接允许
      resolve(true);
    }
  });
}

// 重算开关变化处理
function recalculateSwitchChange(row: CardMapParam) {
  if (!row.isRecalculating) {
    // 关闭重算
    emit("cancelRecalculateTask", row);
  }
}

const STATUS_COLOR: Record<string, string> = {
  RUNNING: "success",
  FAILED: "fail",
  CANCELED: "warning",
  SUBMITTED: "0",
  CANCELLING: "1",
  RECALCULATING: "success", // 重算状态显示为成功色（绿色）
  ADDED: "2",
  UPDATED: "3",
  SAVED: "4",
  DELETED: "5",
};

// 获取当前状态的颜色
function getStatusColor(status: string) {
  return "tag-status-" + STATUS_COLOR[status] || "success";
}

interface CommonMapParam {
  [key: string]: boolean;
}

interface CardMapParam {
  [key: string]: any;
}

// 定义表格数据
const tempTableData = ref<CardMapParam[]>([]);

watch(
  () => props.tableData,
  (newVal) => {
    tempTableData.value = cloneDeep(props.tableData);
    nextTick(() => {
      handleActive();
    });
  },
  { immediate: true, deep: true }
);

function handleActive() {
  // 清空所有卡片的选中状态
  isActive.value = {};
  selectActive.value.length = 0;
  const dataActive: CommonMapParam = {};
  props.tableData?.forEach((item: ProcessTableParam) => {
    const filterIndex = props.selectedValue.findIndex(
      (key: ProcessTableParam) => key.jobName === item.jobName
    );
    if (filterIndex > -1) {
      dataActive[item.jobName] = true;
      selectActive.value.push(item);
    } else {
      dataActive[item.jobName] = false;
    }
  });
  isActive.value = dataActive;
}

// switch切换前二次确定
const beforeChange = async (row: CardMapParam) => {
  const titleTip = row.isRunning
    ? "确定要停止该流程吗？"
    : "确定要运行该流程吗？";
  const cancelTip = row.isRunning ? "取消停止" : "取消运行";
  // 为了配合before-change的类型，这里用try-catch来处理
  try {
    await ElMessageBox.confirm(titleTip, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    return true;
  } catch {
    ElMessage.warning(cancelTip);
    return false;
  }
};

// 选择器切换
async function switchChange(item: CardMapParam) {
  const curSwitch = item.isRunning;
  if (curSwitch) {
    emit("runFlinkTask", item);
  } else {
    emit("cancelFlinkTask", item);
  }
}

const isActive = ref<CommonMapParam>({});

const selectActive = ref<CardMapParam[]>([]);

const NOT_ELEMENT = ["text", "switch"];

function lightClick(item: CardMapParam, event: Event) {
  // 点击卡片时，如果点击的选择器，则不触发高亮
  const bool = NOT_ELEMENT.some((i) =>
    (event.target as HTMLElement).className.includes(i)
  );
  if (bool) return;
  isActive.value[item.jobName] = !isActive.value[item.jobName];
  // 如果当前卡片是选中状态，则添加到选中数组，否则从选中数组中移除
  if (isActive.value[item.jobName]) {
    selectActive.value.push(item);
  } else {
    selectActive.value = selectActive.value.filter(
      (i) => i.jobName !== item.jobName
    );
  }
  emit("handleSelectionChange", selectActive.value);
}

// 勾选框全选
watch(
  () => props.watchAllSelect,
  (val) => {
    if (val) {
      // 全部高亮
      Object.keys(isActive.value).forEach((key) => {
        isActive.value[key] = true;
      });
      selectActive.value = cloneDeep(props.tableData);
    } else {
      // 全部取消高亮
      Object.keys(isActive.value).forEach((key) => {
        isActive.value[key] = false;
      });
      selectActive.value = [];
    }
    emit("handleSelectionChange", selectActive.value);
  }
);

// 监听是否是半选状态,不是则清除所有的高亮
watch(
  () => props.isIndeterminate,
  (val) => {
    if (val) return;
    if (!props.watchAllSelect) {
      // 全部取消高亮
      Object.keys(isActive.value).forEach((key) => {
        isActive.value[key] = false;
      });
      selectActive.value = [];
      emit("handleSelectionChange", selectActive.value);
    }
  }
);
</script>

<style lang="scss" scoped>
.card-container {
  width: 100%;
  height: calc(100% - 42px);
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  align-content: start;
  column-gap: 24px;
  overflow: auto;
  border-bottom: 1px solid #dcdfe6;
  .card-style {
    min-width: 285px;
    height: 234px;
    margin-bottom: 24px;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #e5e6eb;
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      height: 24px;
      line-height: 24px;
      .header-title {
        margin-left: 8px;
        font-weight: 500;
        color: #333333;
        display: inline-block;
        max-width: calc(100% - 30px);
      }
    }
    .content-title {
      display: inline-block;
      max-width: calc(100% - 76px);
      vertical-align: top;
    }
    .card-footer {
      display: flex;
      justify-content: space-between;
      line-height: 24px;
    }
  }
  .highLight {
    border: 2px solid #165dff;
  }
  .card-style:hover {
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
  }
  .text-middle {
    vertical-align: middle;
  }
  .text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .text-pointer {
    cursor: pointer;
  }
  .text-disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}
</style>
