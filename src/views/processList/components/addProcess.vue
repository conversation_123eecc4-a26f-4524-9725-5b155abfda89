<template>
  <el-dialog v-bind="dialogConfig" v-model="show" @close="resetForm">
    <el-form
      ref="ruleFormRef"
      :model="formData"
      label-position="top"
      require-asterisk-position="right"
      :rules="formDataRules"
    >
      <el-form-item label="流程名称" prop="alias">
        <el-input v-model="formData.alias" maxlength="20"></el-input>
      </el-form-item>
      <el-form-item label="作业名称" prop="jobName" v-show="!formData.id">
        <el-input v-model="formData.jobName" maxlength="50"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
// 定义弹窗
const dialogConfig = reactive({
  title: "新增流程",
  width: "360px",
  closeOnClickModal: false,
  closeOnPressEscape: false,
});

const props = defineProps<{
  inputData: ProcessTableParam;
}>();

// 传递子组件的emit
const emit = defineEmits(["refreshList"]);

// 弹窗的打开和关闭,获取父组件的v-model的值
const show = defineModel<boolean>({ default: false });

const formData = ref<ProcessTableParam>({
  alias: "",
  id: "",
  jobName: "",
});

watch(
  () => show.value,
  (val) => {
    if (val) {
      formData.value = cloneDeep(props.inputData);
      dialogConfig.title = props.inputData.id ? "编辑流程" : "新增流程";
    }
  }
);

import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const formDataRules = {
  alias: [
    {
      required: true,
      message: "请输入流程名称",
      trigger: ["blur", "change"],
    },
  ],
  jobName: [
    {
      required: true,
      message: "请输入flink作业名称",
      trigger: ["blur", "change"],
    },
    {
      pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
      message: "只能输入字母数字下划线，且首字符只能是字母",
      trigger: ["blur", "change"],
    },
  ],
};

// 点击确定
async function confirm() {
  // 如果使用async和await获取validate，不会获取到Promise.reject的值，所以需要try catch
  try {
    const valid = await ruleFormRef.value?.validate();
    await (props.inputData?.id ? updateFlinkTask : addFlinkTask)();
  } catch (error) {
    console.log(error);
  }
}

// 接口导入
import customApi from "@/api/custom";

// 新增flink任务
async function addFlinkTask() {
  const param = {
    alias: formData.value.alias,
    jobName: formData.value.jobName,
  };
  try {
    const res = await customApi.addFlinkJob(param);
    ElMessage.success(res.msg);
    show.value = false;
    emit("refreshList");
  } catch (error) {
    console.log(error);
  }
}

// 更新flink任务
async function updateFlinkTask() {
  const param = {
    alias: formData.value.alias,
    jobName: formData.value.jobName,
    id: formData.value.id,
  };
  try {
    const res = await customApi.updateFlinkJob(param);
    ElMessage.success(res.msg);
    show.value = false;
    emit("refreshList");
  } catch (error) {
    console.log(error);
  }
}

// 用于关闭弹窗后重置表单操作
function resetForm() {
  ruleFormRef.value?.resetFields();
}
</script>
<style lang="scss" scoped></style>
