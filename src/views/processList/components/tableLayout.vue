<template>
  <el-table
    :data="tempTableData"
    class="table-style"
    @selection-change="handleSelectionChange"
    row-key="id"
    :tree-props="{ checkStrictly: true }"
    :select-on-indeterminate="false"
    scrollbar-always-on
    ref="tableRef"
  >
    <el-table-column type="selection" width="55" />
    <template v-for="item in processTableHeader">
      <el-table-column v-if="item.prop == 'status'" v-bind="item">
        <template #default="scope">
          <el-tag :class="getStatusColor(scope.row.status)" round>
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column v-else-if="item.prop == 'isRunning'" v-bind="item">
        <template #default="scope">
          <el-switch
            size="large"
            v-model="scope.row.isRunning"
            inline-prompt
            :active-text="disabledClick(scope.row) ? '提交' : '运行 '"
            :inactive-text="disabledClick(scope.row) ? '关闭' : '停止 '"
            :style="{
              '--el-switch-on-color': disabledClick(scope.row)
                ? 'var(--el-color-primary)'
                : '',
              '--el-switch-off-color': disabledClick(scope.row)
                ? 'var(--el-color-primary)'
                : '',
            }"
            style="height: 24px"
            :loading="disabledClick(scope.row)"
            :before-change="() => beforeChange(scope.row)"
            @change="switchChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column v-else-if="item.prop == 'realTimeRunning'" v-bind="item">
        <template #default="scope">
          {{ getRealTimeRunning(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column v-else-if="item.prop == 'recalculateRunning'" v-bind="item">
        <template #default="scope">
          {{ getRecalculateRunning(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column v-else-if="item.prop == 'recalculateStatus'" v-bind="item">
        <template #default="scope">
          <el-tag :class="getRecalculateStatusColor(scope.row.recalculateStatus)" round>
            {{ getRecalculateStatusText(scope.row.recalculateStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column v-else-if="item.prop == 'isRecalculating'" v-bind="item">
        <template #default="scope">
          <el-switch
            size="large"
            v-model="scope.row.isRecalculating"
            inline-prompt
            :active-text="disabledRecalculateClick(scope.row) ? '提交' : '运行 '"
            :inactive-text="disabledRecalculateClick(scope.row) ? '关闭' : '停止 '"
            :style="{
              '--el-switch-on-color': disabledRecalculateClick(scope.row)
                ? 'var(--el-color-primary)'
                : '',
              '--el-switch-off-color': disabledRecalculateClick(scope.row)
                ? 'var(--el-color-primary)'
                : '',
            }"
            style="height: 24px"
            :loading="disabledRecalculateClick(scope.row)"
            :before-change="beforeRecalculateChange.bind(null, scope.row)"
            @change="recalculateSwitchChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column v-else-if="item.prop == 'processDesign'" v-bind="item">
        <template #default="scope">
          <el-button link type="primary" @click="routeClick(scope.row, 'view')">
            查看流程
          </el-button>
          <el-button
            link
            type="primary"
            @click="routeClick(scope.row, 'design')"
            :disabled="disabledDesignClick(scope.row)"
          >
            流程设计
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        v-else
        v-bind="item"
        show-overflow-tooltip
      ></el-table-column>
    </template>
    <el-table-column fixed="right" label="操作" width="150" align="center">
      <template #default="scope">
        <el-button
          link
          type="primary"
          @click="handleClick(scope.row)"
          :disabled="disabledDesignClick(scope.row)"
        >
          编辑
        </el-button>
        <el-button
          link
          type="danger"
          @click="deleteClick(scope.row)"
          :disabled="disabledDesignClick(scope.row)"
        >
          删除
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
const props = defineProps<{
  tableData: ProcessTableParam[];
  selectedValue: ProcessTableParam[];
}>();

const emit = defineEmits([
  "routeClick",
  "handleClick",
  "deleteClick",
  "recalculateClick",
  "cancelRecalculateTask",
  "handleSelectionChange",
  "refreshList",
  "runFlinkTask",
  "cancelFlinkTask",
]);

import { common } from "@/utils/common";

// 定义流程列表表表头信息
const processTableHeader = [
  {
    label: "序号",
    type: "index",
    width: "62",
    align: "center",
  },
  {
    prop: "alias",
    label: "流程名称",
    minWidth: "150",
    formatter: common.formatText(),
  },
  {
    prop: "jobName",
    label: "作业名称",
    minWidth: "150",
    formatter: common.formatText(),
  },
  {
    prop: "updateTime",
    label: "最后修改时间",
    minWidth: "150",
    formatter: common.formatterTime(),
  },
  {
    prop: "realTimeRunning",
    label: "实时运行时间",
    minWidth: "150",
  },
  {
    prop: "recalculateRunning",
    label: "重算运行时间",
    minWidth: "150",
  },
  {
    prop: "status",
    label: "实时状态",
    minWidth: "120",
    formatter: common.formatText(),
  },
  {
    prop: "recalculateStatus",
    label: "重算状态",
    minWidth: "120",
  },
  {
    prop: "isRunning",
    label: "实时计算操作",
    minWidth: "100",
  },
  {
    prop: "isRecalculating",
    label: "重算操作",
    minWidth: "100",
  },
  {
    prop: "processDesign",
    label: "流程设计",
    width: "180",
    align: "center",
  },
];

const handleSelectionChange = (val: ProcessTableParam[]) => {
  emit("handleSelectionChange", val);
};

// 路由跳转
function routeClick(row: ProcessTableParam, type: string) {
  emit("routeClick", row, type);
}

// 流程设计按钮的置灰处理
const DISABLE_STATUS_DATA = ["RUNNING", "CANCELLING", "SUBMITTED"];

function disabledDesignClick(row: ProcessTableParam) {
  if (!row.status) return false;
  return DISABLE_STATUS_DATA.includes(row.status);
}

// 运行按钮的置灰处理
const DISABLED_DATA = ["CANCELLING", "SUBMITTED"];

function disabledClick(row: ProcessTableParam) {
  if (!row.status) return false;
  return DISABLED_DATA.includes(row.status);
}

// 重算按钮的置灰处理
function disabledRecalculateClick(row: ProcessTableParam) {
  if (!row.recalculateStatus) return false;
  return DISABLED_DATA.includes(row.recalculateStatus);
}

// 判断是否显示"提交"状态（RECALCULATING状态应显示"运行"而不是"提交"）
function isSubmittingStatus(row: ProcessTableParam) {
  if (!row.status) return false;
  const SUBMITTING_STATUS = ["CANCELLING", "SUBMITTED"];
  return SUBMITTING_STATUS.includes(row.status);
}



// 定义表格数据
const tempTableData = ref<ProcessTableParam[]>([]);

watch(
  () => props.tableData,
  (newVal) => {
    if (newVal) {
      tempTableData.value = cloneDeep(props.tableData);
      nextTick(() => {
        getCheckedData();
      });
    }
  },
  { immediate: true, deep: true }
);

const tableRef = ref();

// 已勾选的数据进行回显
function getCheckedData() {
  const selected = props.selectedValue?.map((item: ProcessTableParam) => {
    return item.jobName;
  });
  tempTableData.value?.forEach((item: ProcessTableParam) => {
    tableRef.value!.toggleRowSelection(item, selected?.includes(item.jobName));
  });
}

// switch切换前二次确定
const beforeChange = async (row: ProcessTableParam) => {
  const titleTip = row.isRunning
    ? "确定要停止该流程吗？"
    : "确定要运行该流程吗？";
  const cancelTip = row.isRunning ? "取消停止" : "取消运行";
  // 为了配合before-change的类型，这里用try-catch来处理
  try {
    await ElMessageBox.confirm(titleTip, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    return true;
  } catch {
    ElMessage.warning(cancelTip);
    return false;
  }
};

// 选择器切换
async function switchChange(item: ProcessTableParam) {
  const curSwitch = item.isRunning;
  if (curSwitch) {
    emit("runFlinkTask", item);
  } else {
    emit("cancelFlinkTask", item);
  }
}

// 编辑
function handleClick(row: ProcessTableParam) {
  emit("handleClick", row);
}

// 删除
function deleteClick(row: ProcessTableParam) {
  emit("deleteClick", row);
}

function recalculateClick(row: ProcessTableParam) {
  emit("recalculateClick", row);
}

// 重算开关变化前的处理
function beforeRecalculateChange(row: ProcessTableParam): Promise<boolean> {
  return new Promise<boolean>((resolve) => {
    if (!row.isRecalculating) {
      // 要开启重算，显示重算弹窗
      emit("recalculateClick", row);
      resolve(false); // 阻止开关自动切换，由弹窗确认后手动设置
    } else {
      // 要关闭重算，直接允许
      resolve(true);
    }
  });
}

// 重算开关变化处理
function recalculateSwitchChange(row: ProcessTableParam) {
  const curSwitch = row.isRecalculating;
  if (curSwitch) {
    // 开启重算 - 这种情况不应该发生，因为beforeRecalculateChange会阻止
    emit("recalculateClick", row);
  } else {
    // 关闭重算
    emit("cancelRecalculateTask", row);
  }
}

const STATUS_COLOR: Record<string, string> = {
  RUNNING: "success",
  FAILED: "fail",
  CANCELED: "warning",
  SUBMITTED: "0",
  CANCELLING: "1",
  RECALCULATING: "success", // 重算状态显示为成功色（绿色）
  ADDED: "2",
  UPDATED: "3",
  SAVED: "4",
  DELETED: "5",
};

// 获取实时运行时间
function getRealTimeRunning(row: ProcessTableParam) {
  // 当流程任务是正常运行提交成功时，显示时间；其他状态显示 "--"
  const SUCCESS_RUNNING_STATUS = ["RUNNING"];

  if (row.status && SUCCESS_RUNNING_STATUS.includes(row.status)) {
    // 如果有专门的运行时间字段，使用它；否则使用更新时间
    const timeValue = row.runningTime || row.updateTime;
    return timeValue ? common.formatterTime()(row, null, timeValue) : "--";
  }

  return "--";
}

// 获取重算运行时间
function getRecalculateRunning(row: ProcessTableParam) {
  // 当重算状态是RUNNING时，显示时间；其他状态显示 "--"
  const RECALCULATE_RUNNING_STATUS = ["RUNNING"];
  if (row.recalculateStatus && RECALCULATE_RUNNING_STATUS.includes(row.recalculateStatus)) {
    // 如果有专门的重算运行时间字段，使用它；否则使用更新时间
    const timeValue = row.recalculateRunningTime || row.updateTime;
    return timeValue ? common.formatterTime()(row, null, timeValue) : "--";
  }

  return "--";
}

// 获取当前状态的颜色
function getStatusColor(status: string) {
  if (status === "RECALCULATING") {
    status = "CANCELED";
  }
  return "tag-status-" + STATUS_COLOR[status] || "success";
}

// 获取重算状态显示文本
function getRecalculateStatusText(recalculateStatus: string) {

  if (!recalculateStatus) return "--";

  // 重算状态为RECALCULATING时显示"running"
  if (recalculateStatus === "RECALCULATING") {
    return "RUNNING";
  }

  return recalculateStatus;
}

// 获取重算状态颜色
function getRecalculateStatusColor(recalculateStatus: string) {
  if (!recalculateStatus) return "tag-status-default";

  // 重算状态为RECALCULATING时显示成功色（绿色）
  if (recalculateStatus === "RECALCULATING") {
    return "tag-status-success";
  }

  return "tag-status-" + STATUS_COLOR[recalculateStatus] || "tag-status-default";
}
</script>

<style scoped>
.table-style {
  width: 100%;
  height: calc(100% - 42px);
}
</style>
