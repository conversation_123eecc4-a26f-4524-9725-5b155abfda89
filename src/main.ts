import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import { createPinia } from "pinia";
import ElementPlus from "element-plus";
import locale from "element-plus/es/locale/lang/zh-cn";
// 使用vue-flow需要添加的样式
import "@vue-flow/core/dist/style.css";
import "@vue-flow/core/dist/theme-default.css";
import "@vue-flow/controls/dist/style.css";
import "@vue-flow/minimap/dist/style.css";

// 引入tdesign-vue-next中的数字输入框
import "tdesign-vue-next/es/style/index.css";

import "./resources/index.scss";

// 引入svgIcon组件
import SvgIcon from "./components/svgIcon/index.vue";

const pinia = createPinia();

const app = createApp(App);

app.use(pinia);

app.use(router);

// 使用Element Plus并设置中文语言
app.use(ElementPlus, { locale });

// 全局注册
app.component("svg-icon", SvgIcon);

app.mount("#app");
