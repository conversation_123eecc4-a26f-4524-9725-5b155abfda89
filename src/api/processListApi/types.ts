// 入参类型定义
export interface AddFlinkJobParam {
  alias: string;
  jobName: string;
}

export interface QueryFlinkJobParam {
  index: number;
  limit: number;
  jobAlias?: string;
}

export interface UpdateFlinkJobParam {
  alias: string;
  jobName: string;
  id: string | number;
}

export interface DeleteFlinkJobParam {
  id: string | number;
}

export interface RunFlinkJobParam {
  jobName: string;
  id: string | number;
  argues?: string;
}

export interface CancelFlinkJobParam {
  jobName: string;
  id: string | number;
  jobId: string;
}

export interface RecalculateFlinkJobParam {
  id: string | number;
  recalculateId: string;
  jobName: string;
  startTime: string | number;
  endTime: string | number;
  variables?: VariableParam[];
}

// Logger相关类型定义
export interface LoggerInfo {
  loggerName: string;
  logLevel: string;
}

// Flink Logger API返回格式（不遵循CommonDataResponse格式）
export interface FlinkLoggerResponse {
  loggerLevelInfos: LoggerInfo[];
}

// 返回值类型定义
export interface QueryFlinkResponse
  extends CommonDataResponse<ProcessTableParam[]> {
  total: number;
}
