import fetch from "@/utils/fetch";
import {
  AddFlinkJobParam,
  QueryFlinkJobParam,
  UpdateFlinkJobParam,
  DeleteFlinkJobParam,
  RunFlinkJobParam,
  CancelFlinkJobParam,
  RecalculateFlinkJobParam,
  QueryFlinkResponse,
} from "./types";

import { type AxiosResponse } from "axios";

// 新增flink任务
export function addFlinkJob(data: AddFlinkJobParam): PromisedCommonResp {
  return fetch({
    url: "/flink-job/add",
    method: "POST",
    data,
  });
}

// 查询flink任务
export function queryFlinkJob(
  data: QueryFlinkJobParam
): Promise<QueryFlinkResponse> {
  return fetch({
    url: `/flink-job/query`,
    method: "POST",
    data,
    timeout: 60000,
  });
}

// 修改flink任务
export function updateFlinkJob(data: UpdateFlinkJobParam): PromisedCommonResp {
  return fetch({
    url: "/flink-job/update",
    method: "PUT",
    data,
  });
}

// 删除flink任务
export function deleteFlinkJob(data: DeleteFlinkJobParam): PromisedCommonResp {
  return fetch({
    url: `/flink-job/delete?id=${data.id}`,
    method: "DELETE",
  });
}

// 运行flink任务
export function runFlinkJob(data: RunFlinkJobParam): PromisedCommonResp {
  return fetch({
    url: "/flink-job/run",
    method: "POST",
    data,
    timeout: 120000,
  });
}

// 取消flink任务
export function cancelFlinkJob(data: CancelFlinkJobParam): PromisedCommonResp {
  return fetch({
    url: "/flink-job/cancel",
    method: "POST",
    data,
    timeout: 120000,
  });
}

// 导出流程信息
export function exportRulesFile(
  data: (number | string)[]
): Promise<AxiosResponse> {
  return fetch({
    url: "/flink-job/exportRulesFile",
    method: "POST",
    data,
  });
}

// 导入流程信息
export function importRulesFile(data: FormData): PromisedCommonResp {
  return fetch({
    url: "/flink-job/importRulesFile",
    method: "POST",
    data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

// 重算flink任务
export function recalculateFlinkJob(data: RecalculateFlinkJobParam): PromisedCommonResp {
  return fetch({
    url: "/flink-job/recalculate",
    method: "POST",
    data,
    timeout: 120000,
  });
}
