// 日志级别管理相关 API 示例
import { ref } from 'vue';
import fetch from "@/utils/fetch";
import {
  AddFlinkJobParam,
  QueryFlinkJobParam,
  UpdateFlinkJobParam,
  DeleteFlinkJobParam,
  RunFlinkJobParam,
  CancelFlinkJobParam,
  RecalculateFlinkJobParam,
  QueryFlinkResponse,
  LoggerInfo,
  FlinkLoggerResponse,
} from "./types";

import { type AxiosResponse } from "axios";

// 获取 logger 列表
// 注意：此API直接返回Flink格式，不是标准的CommonDataResponse格式
export function fetchLoggerLevelList(taskManagerId?: string): Promise<FlinkLoggerResponse> {
  // 使用默认的 taskManagerId 或传入的参数
  const defaultTaskManagerId = taskManagerId || 'localhost:43463-412508';
  return fetch({
    url: `/taskmanagers/${defaultTaskManagerId}/loggers`,
    method: "GET",
  });
}


// 更新日志级别
export function updateLoggerLevel(loggerName: string, logLevel: string, taskManagerId?: string): PromisedCommonResp<{ success: boolean }> {
  const defaultTaskManagerId = taskManagerId || 'localhost:43463-412508';
  return fetch({
    url: `/taskmanagers/${defaultTaskManagerId}/updateLoggersLevel`,
    method: "POST",
    data: {
      loggerLevelInfos: [
        {
          loggerName,
          logLevel
        }
      ]
    },
    headers: {
      "Content-Type": "application/json",
    },
  });
}

// 批量更新日志级别
export function batchUpdateLoggerLevel(loggerLevelInfos: LoggerInfo[], taskManagerId?: string): PromisedCommonResp<{ success: boolean }> {
  const defaultTaskManagerId = taskManagerId || 'localhost:43463-412508';
  return fetch({
    url: `/taskmanagers/${defaultTaskManagerId}/updateLoggersLevel`,
    method: "POST",
    data: {
      loggerLevelInfos
    },
    headers: {
      "Content-Type": "application/json",
    },
  });
}
