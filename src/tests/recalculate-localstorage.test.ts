import { describe, it, expect, beforeEach, vi } from 'vitest'

// 模拟 localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

// 将 mock 赋值给全局对象
Object.defineProperty(globalThis, 'localStorage', {
  value: localStorageMock
})

// 模拟数据结构
interface VariableParam {
  name: string;
  label: string;
  value: string;
}

interface ProcessTableParam {
  id: string | number;
  jobId?: string;
  jobName: string;
  lastRecalculateStartTime?: string;
  lastRecalculateVariables?: VariableParam[];
}

// 模拟重算点击事件的逻辑
function recalculateClick(row: ProcessTableParam) {
  // 从本地存储中加载历史重算参数
  const savedParams = localStorage.getItem(`recalculate_params_${row.id}`);
  if (savedParams) {
    try {
      const params = JSON.parse(savedParams);
      row.lastRecalculateStartTime = params.startTime;
      row.lastRecalculateVariables = params.variables;
    } catch (error) {
      console.warn('解析重算参数失败:', error);
    }
  }
  return row;
}

// 模拟重算确认事件的逻辑
function handleRecalculateConfirm(data: any) {
  // 保存重算参数到本地存储，用于下次回显
  const recalculateParams = {
    startTime: data.startTime,
    variables: data.variables || []
  };
  localStorage.setItem(`recalculate_params_${data.id}`, JSON.stringify(recalculateParams));
  return recalculateParams;
}

describe('重算参数本地存储功能', () => {
  beforeEach(() => {
    // 清除所有 mock 调用记录
    vi.clearAllMocks()
  })

  it('应该正确保存重算参数到本地存储', () => {
    const testData = {
      id: 'test8',
      startTime: '2024-01-10',
      endTime: '2024-01-15',
      variables: [
        { name: '租户id', label: 'tenantId', value: '1' },
        { name: '用户id', label: 'userId', value: '123' }
      ]
    }

    const result = handleRecalculateConfirm(testData)

    expect(localStorage.setItem).toHaveBeenCalledWith(
      'recalculate_params_test8',
      JSON.stringify({
        startTime: '2024-01-10',
        variables: [
          { name: '租户id', label: 'tenantId', value: '1' },
          { name: '用户id', label: 'userId', value: '123' }
        ]
      })
    )

    expect(result).toEqual({
      startTime: '2024-01-10',
      variables: [
        { name: '租户id', label: 'tenantId', value: '1' },
        { name: '用户id', label: 'userId', value: '123' }
      ]
    })
  })

  it('应该正确从本地存储中加载重算参数', () => {
    const mockSavedData = JSON.stringify({
      startTime: '2024-01-10',
      variables: [
        { name: '租户id', label: 'tenantId', value: '1' },
        { name: '用户id', label: 'userId', value: '123' }
      ]
    })

    localStorageMock.getItem.mockReturnValue(mockSavedData)

    const testRow: ProcessTableParam = {
      id: 'test8',
      jobName: 'test_job'
    }

    const result = recalculateClick(testRow)

    expect(localStorage.getItem).toHaveBeenCalledWith('recalculate_params_test8')
    expect(result.lastRecalculateStartTime).toBe('2024-01-10')
    expect(result.lastRecalculateVariables).toEqual([
      { name: '租户id', label: 'tenantId', value: '1' },
      { name: '用户id', label: 'userId', value: '123' }
    ])
  })

  it('应该处理本地存储中没有数据的情况', () => {
    localStorageMock.getItem.mockReturnValue(null)

    const testRow: ProcessTableParam = {
      id: 'test9',
      jobName: 'test_job'
    }

    const result = recalculateClick(testRow)

    expect(localStorage.getItem).toHaveBeenCalledWith('recalculate_params_test9')
    expect(result.lastRecalculateStartTime).toBeUndefined()
    expect(result.lastRecalculateVariables).toBeUndefined()
  })

  it('应该处理本地存储中数据格式错误的情况', () => {
    localStorageMock.getItem.mockReturnValue('invalid json')

    const testRow: ProcessTableParam = {
      id: 'test10',
      jobName: 'test_job'
    }

    // 模拟 console.warn
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

    const result = recalculateClick(testRow)

    expect(localStorage.getItem).toHaveBeenCalledWith('recalculate_params_test10')
    expect(consoleSpy).toHaveBeenCalledWith('解析重算参数失败:', expect.any(Error))
    expect(result.lastRecalculateStartTime).toBeUndefined()
    expect(result.lastRecalculateVariables).toBeUndefined()

    consoleSpy.mockRestore()
  })

  it('应该处理没有变量的情况', () => {
    const testData = {
      id: 'test11',
      startTime: '2024-01-10',
      endTime: '2024-01-15'
      // 没有 variables 字段
    }

    const result = handleRecalculateConfirm(testData)

    expect(localStorage.setItem).toHaveBeenCalledWith(
      'recalculate_params_test11',
      JSON.stringify({
        startTime: '2024-01-10',
        variables: []
      })
    )

    expect(result.variables).toEqual([])
  })
})