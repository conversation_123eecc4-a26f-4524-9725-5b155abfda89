import { describe, it, expect } from 'vitest'

// 模拟数据结构
interface VariableParam {
  name: string;
  label: string;
  value: string;
}

interface ProcessTableParam {
  id: string | number;
  jobId?: string;
  jobName: string;
  lastRecalculateStartTime?: string;
  lastRecalculateVariables?: VariableParam[];
}

// 模拟初始化逻辑
function initFormWithEcho(rowData?: ProcessTableParam) {
  const today = '2024-01-15'
  
  // 检查是否有历史重算参数进行回显
  const hasLastRecalculateData = rowData?.lastRecalculateStartTime || rowData?.lastRecalculateVariables?.length

  const form = {
    startTime: hasLastRecalculateData ? (rowData?.lastRecalculateStartTime || '') : '',
    endTime: today
  }

  // 回显变量数据
  let variables: VariableParam[] = []
  if (hasLastRecalculateData && rowData?.lastRecalculateVariables?.length) {
    variables = [...rowData.lastRecalculateVariables]
  }

  return { form, variables }
}

describe('重算设置回显功能', () => {
  it('应该正确回显历史开始时间和变量', () => {
    const mockRowData: ProcessTableParam = {
      id: '1',
      jobName: 'test_job',
      lastRecalculateStartTime: '2024-01-10',
      lastRecalculateVariables: [
        { name: '租户id', label: 'tenantId', value: '1' },
        { name: '用户id', label: 'userId', value: '123' }
      ]
    }

    const { form, variables } = initFormWithEcho(mockRowData)

    expect(form.startTime).toBe('2024-01-10')
    expect(form.endTime).toBe('2024-01-15')
    expect(variables).toHaveLength(2)
    expect(variables[0]).toEqual({ name: '租户id', label: 'tenantId', value: '1' })
    expect(variables[1]).toEqual({ name: '用户id', label: 'userId', value: '123' })
  })

  it('应该在没有历史数据时使用默认值', () => {
    const mockRowData: ProcessTableParam = {
      id: '1',
      jobName: 'test_job'
    }

    const { form, variables } = initFormWithEcho(mockRowData)

    expect(form.startTime).toBe('')
    expect(form.endTime).toBe('2024-01-15')
    expect(variables).toHaveLength(0)
  })

  it('应该在没有rowData时使用默认值', () => {
    const { form, variables } = initFormWithEcho()

    expect(form.startTime).toBe('')
    expect(form.endTime).toBe('2024-01-15')
    expect(variables).toHaveLength(0)
  })

  it('应该在只有开始时间没有变量时正确处理', () => {
    const mockRowData: ProcessTableParam = {
      id: '1',
      jobName: 'test_job',
      lastRecalculateStartTime: '2024-01-10'
    }

    const { form, variables } = initFormWithEcho(mockRowData)

    expect(form.startTime).toBe('2024-01-10')
    expect(form.endTime).toBe('2024-01-15')
    expect(variables).toHaveLength(0)
  })

  it('应该在只有变量没有开始时间时正确处理', () => {
    const mockRowData: ProcessTableParam = {
      id: '1',
      jobName: 'test_job',
      lastRecalculateVariables: [
        { name: '租户id', label: 'tenantId', value: '1' }
      ]
    }

    const { form, variables } = initFormWithEcho(mockRowData)

    expect(form.startTime).toBe('')
    expect(form.endTime).toBe('2024-01-15')
    expect(variables).toHaveLength(1)
    expect(variables[0]).toEqual({ name: '租户id', label: 'tenantId', value: '1' })
  })
})