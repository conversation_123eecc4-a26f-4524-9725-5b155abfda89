import { describe, it, expect } from 'vitest'

describe('RECALCULATING Display Logic', () => {
  // 模拟状态检查函数
  function isSubmittingStatus(status: string) {
    const SUBMITTING_STATUS = ["CANCELLING", "SUBMITTED"];
    return SUBMITTING_STATUS.includes(status);
  }

  function disabledClick(status: string) {
    const DISABLED_DATA = ["CANCELLING", "SUBMITTED", "RECALCULATING"];
    return DISABLED_DATA.includes(status);
  }

  // 模拟获取显示文本的函数
  function getActiveText(status: string) {
    return isSubmittingStatus(status) ? '提交' : '运行';
  }

  function getInactiveText(status: string) {
    return isSubmittingStatus(status) ? '关闭' : '停止';
  }

  it('should show "运行" for RECALCULATING status instead of "提交"', () => {
    const status = 'RECALCULATING';
    
    expect(getActiveText(status)).toBe('运行');
    expect(getInactiveText(status)).toBe('停止');
  });

  it('should show "提交" for SUBMITTED status', () => {
    const status = 'SUBMITTED';
    
    expect(getActiveText(status)).toBe('提交');
    expect(getInactiveText(status)).toBe('关闭');
  });

  it('should show "提交" for CANCELLING status', () => {
    const status = 'CANCELLING';
    
    expect(getActiveText(status)).toBe('提交');
    expect(getInactiveText(status)).toBe('关闭');
  });

  it('should show "运行" for RUNNING status', () => {
    const status = 'RUNNING';
    
    expect(getActiveText(status)).toBe('运行');
    expect(getInactiveText(status)).toBe('停止');
  });

  it('should show "运行" for FAILED status', () => {
    const status = 'FAILED';
    
    expect(getActiveText(status)).toBe('运行');
    expect(getInactiveText(status)).toBe('停止');
  });

  it('should disable switch for RECALCULATING but show running text', () => {
    const status = 'RECALCULATING';
    
    // 开关应该被禁用
    expect(disabledClick(status)).toBe(true);
    
    // 但显示文本应该是"运行"而不是"提交"
    expect(isSubmittingStatus(status)).toBe(false);
    expect(getActiveText(status)).toBe('运行');
  });

  it('should separate disabled logic from display logic', () => {
    const testCases = [
      { status: 'RECALCULATING', disabled: true, submitting: false, activeText: '运行' },
      { status: 'SUBMITTED', disabled: true, submitting: true, activeText: '提交' },
      { status: 'CANCELLING', disabled: true, submitting: true, activeText: '提交' },
      { status: 'RUNNING', disabled: false, submitting: false, activeText: '运行' },
      { status: 'FAILED', disabled: false, submitting: false, activeText: '运行' },
    ];

    testCases.forEach(({ status, disabled, submitting, activeText }) => {
      expect(disabledClick(status)).toBe(disabled);
      expect(isSubmittingStatus(status)).toBe(submitting);
      expect(getActiveText(status)).toBe(activeText);
    });
  });

  it('should handle loading state correctly', () => {
    // loading状态应该只对真正的提交状态生效
    expect(isSubmittingStatus('RECALCULATING')).toBe(false); // 不显示loading
    expect(isSubmittingStatus('SUBMITTED')).toBe(true);      // 显示loading
    expect(isSubmittingStatus('CANCELLING')).toBe(true);     // 显示loading
  });
})
