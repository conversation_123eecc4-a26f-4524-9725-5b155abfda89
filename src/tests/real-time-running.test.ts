import { describe, it, expect, vi } from 'vitest'

describe('Real Time Running Display Logic', () => {
  // 模拟common.formatterTime函数
  const mockFormatterTime = vi.fn(() => (row: any, column: any, cellValue: any, index: any) => {
    if (!cellValue) return '--';
    return `格式化时间: ${cellValue}`;
  });

  // 模拟common对象
  const common = {
    formatterTime: mockFormatterTime
  };

  // 模拟getRealTimeRunning函数
  function getRealTimeRunning(row: any) {
    // 当流程任务是正常运行提交成功时，显示时间；其他状态显示 "--"
    const SUCCESS_RUNNING_STATUS = ["RUNNING"];
    
    if (row.status && SUCCESS_RUNNING_STATUS.includes(row.status)) {
      // 如果有专门的运行时间字段，使用它；否则使用更新时间
      const timeValue = row.runningTime || row.updateTime;
      return timeValue ? common.formatterTime()(row, null, timeValue, null) : "--";
    }
    
    return "--";
  }

  // 测试数据
  const testCases = [
    {
      name: 'RUNNING状态且有runningTime',
      data: { 
        id: 1, 
        status: 'RUNNING', 
        runningTime: '2024-01-01 10:00:00',
        updateTime: '2024-01-01 09:00:00'
      },
      expected: '格式化时间: 2024-01-01 10:00:00'
    },
    {
      name: 'RUNNING状态但无runningTime，有updateTime',
      data: { 
        id: 2, 
        status: 'RUNNING', 
        updateTime: '2024-01-01 09:00:00'
      },
      expected: '格式化时间: 2024-01-01 09:00:00'
    },
    {
      name: 'RUNNING状态但无任何时间',
      data: { 
        id: 3, 
        status: 'RUNNING'
      },
      expected: '--'
    },
    {
      name: 'SUBMITTED状态',
      data: { 
        id: 4, 
        status: 'SUBMITTED', 
        runningTime: '2024-01-01 10:00:00'
      },
      expected: '--'
    },
    {
      name: 'RECALCULATING状态',
      data: { 
        id: 5, 
        status: 'RECALCULATING', 
        runningTime: '2024-01-01 10:00:00'
      },
      expected: '--'
    },
    {
      name: 'FAILED状态',
      data: { 
        id: 6, 
        status: 'FAILED', 
        runningTime: '2024-01-01 10:00:00'
      },
      expected: '--'
    },
    {
      name: 'CANCELED状态',
      data: { 
        id: 7, 
        status: 'CANCELED', 
        runningTime: '2024-01-01 10:00:00'
      },
      expected: '--'
    },
    {
      name: '无状态',
      data: { 
        id: 8, 
        runningTime: '2024-01-01 10:00:00'
      },
      expected: '--'
    }
  ];

  testCases.forEach(({ name, data, expected }) => {
    it(`should handle ${name}`, () => {
      const result = getRealTimeRunning(data);
      expect(result).toBe(expected);
    });
  });

  it('should prioritize runningTime over updateTime when both exist', () => {
    const data = {
      id: 1,
      status: 'RUNNING',
      runningTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 09:00:00'
    };

    const result = getRealTimeRunning(data);
    expect(result).toBe('格式化时间: 2024-01-01 10:00:00');
  });

  it('should only show time for RUNNING status', () => {
    const statuses = ['RUNNING', 'SUBMITTED', 'RECALCULATING', 'FAILED', 'CANCELED', 'CANCELLING'];
    const timeValue = '2024-01-01 10:00:00';

    statuses.forEach(status => {
      const data = { id: 1, status, runningTime: timeValue };
      const result = getRealTimeRunning(data);
      
      if (status === 'RUNNING') {
        expect(result).toBe('格式化时间: 2024-01-01 10:00:00');
      } else {
        expect(result).toBe('--');
      }
    });
  });

  it('should handle edge cases', () => {
    // 空对象
    expect(getRealTimeRunning({})).toBe('--');
    
    // null状态
    expect(getRealTimeRunning({ status: null, runningTime: '2024-01-01 10:00:00' })).toBe('--');
    
    // 空字符串状态
    expect(getRealTimeRunning({ status: '', runningTime: '2024-01-01 10:00:00' })).toBe('--');
    
    // undefined状态
    expect(getRealTimeRunning({ status: undefined, runningTime: '2024-01-01 10:00:00' })).toBe('--');
  });
})
