import { describe, it, expect, vi } from 'vitest'

describe('Running Time Columns Independence', () => {
  // 模拟common.formatterTime函数
  const mockFormatterTime = vi.fn(() => (row: any, column: any, cellValue: any, index: any) => {
    if (!cellValue) return '--';
    return `格式化时间: ${cellValue}`;
  });

  // 模拟common对象
  const common = {
    formatterTime: mockFormatterTime
  };

  // 实时运行时间函数
  function getRealTimeRunning(row: any) {
    const SUCCESS_RUNNING_STATUS = ["RUNNING"];
    
    if (row.status && SUCCESS_RUNNING_STATUS.includes(row.status)) {
      const timeValue = row.runningTime || row.updateTime;
      return timeValue ? common.formatterTime()(row, null, timeValue, null) : "--";
    }
    
    return "--";
  }

  // 重算运行时间函数
  function getRecalculateRunning(row: any) {
    const RECALCULATE_RUNNING_STATUS = ["RECALCULATING"];
    
    if (row.status && RECALCULATE_RUNNING_STATUS.includes(row.status)) {
      const timeValue = row.recalculateRunningTime || row.updateTime;
      return timeValue ? common.formatterTime()(row, null, timeValue, null) : "--";
    }
    
    return "--";
  }

  // 测试数据集
  const testScenarios = [
    {
      name: 'RUNNING状态 - 只显示实时运行时间',
      data: {
        id: 1,
        status: 'RUNNING',
        runningTime: '2024-01-01 10:00:00',
        recalculateRunningTime: '2024-01-01 11:00:00',
        updateTime: '2024-01-01 09:00:00'
      },
      expectedRealTime: '格式化时间: 2024-01-01 10:00:00',
      expectedRecalculateTime: '--'
    },
    {
      name: 'RECALCULATING状态 - 只显示重算运行时间',
      data: {
        id: 2,
        status: 'RECALCULATING',
        runningTime: '2024-01-01 10:00:00',
        recalculateRunningTime: '2024-01-01 11:00:00',
        updateTime: '2024-01-01 09:00:00'
      },
      expectedRealTime: '--',
      expectedRecalculateTime: '格式化时间: 2024-01-01 11:00:00'
    },
    {
      name: 'SUBMITTED状态 - 两个时间都不显示',
      data: {
        id: 3,
        status: 'SUBMITTED',
        runningTime: '2024-01-01 10:00:00',
        recalculateRunningTime: '2024-01-01 11:00:00',
        updateTime: '2024-01-01 09:00:00'
      },
      expectedRealTime: '--',
      expectedRecalculateTime: '--'
    },
    {
      name: 'FAILED状态 - 两个时间都不显示',
      data: {
        id: 4,
        status: 'FAILED',
        runningTime: '2024-01-01 10:00:00',
        recalculateRunningTime: '2024-01-01 11:00:00',
        updateTime: '2024-01-01 09:00:00'
      },
      expectedRealTime: '--',
      expectedRecalculateTime: '--'
    },
    {
      name: 'RUNNING状态但无专用时间字段 - 回退到updateTime',
      data: {
        id: 5,
        status: 'RUNNING',
        updateTime: '2024-01-01 09:00:00'
      },
      expectedRealTime: '格式化时间: 2024-01-01 09:00:00',
      expectedRecalculateTime: '--'
    },
    {
      name: 'RECALCULATING状态但无专用时间字段 - 回退到updateTime',
      data: {
        id: 6,
        status: 'RECALCULATING',
        updateTime: '2024-01-01 09:00:00'
      },
      expectedRealTime: '--',
      expectedRecalculateTime: '格式化时间: 2024-01-01 09:00:00'
    }
  ];

  testScenarios.forEach(({ name, data, expectedRealTime, expectedRecalculateTime }) => {
    it(`should handle ${name}`, () => {
      const realTimeResult = getRealTimeRunning(data);
      const recalculateTimeResult = getRecalculateRunning(data);
      
      expect(realTimeResult).toBe(expectedRealTime);
      expect(recalculateTimeResult).toBe(expectedRecalculateTime);
    });
  });

  it('should maintain independence between the two time columns', () => {
    // 验证两个时间列完全独立，互不影响
    const data = {
      id: 1,
      status: 'RUNNING', // 只有实时运行时间应该显示
      runningTime: '2024-01-01 10:00:00',
      recalculateRunningTime: '2024-01-01 11:00:00'
    };

    expect(getRealTimeRunning(data)).toBe('格式化时间: 2024-01-01 10:00:00');
    expect(getRecalculateRunning(data)).toBe('--');

    // 改变状态为RECALCULATING
    data.status = 'RECALCULATING';
    
    expect(getRealTimeRunning(data)).toBe('--');
    expect(getRecalculateRunning(data)).toBe('格式化时间: 2024-01-01 11:00:00');
  });

  it('should handle time field priority correctly for each column', () => {
    const data = {
      id: 1,
      status: 'RUNNING',
      runningTime: '2024-01-01 10:00:00',
      recalculateRunningTime: '2024-01-01 11:00:00',
      updateTime: '2024-01-01 09:00:00'
    };

    // 实时运行时间应该优先使用runningTime
    expect(getRealTimeRunning(data)).toBe('格式化时间: 2024-01-01 10:00:00');

    // 改为RECALCULATING状态
    data.status = 'RECALCULATING';
    
    // 重算运行时间应该优先使用recalculateRunningTime
    expect(getRecalculateRunning(data)).toBe('格式化时间: 2024-01-01 11:00:00');
  });

  it('should handle missing time fields gracefully', () => {
    // 测试缺少时间字段的情况
    const scenarios = [
      { status: 'RUNNING', hasRunningTime: false, hasRecalculateTime: false, hasUpdateTime: true },
      { status: 'RECALCULATING', hasRunningTime: false, hasRecalculateTime: false, hasUpdateTime: true },
      { status: 'RUNNING', hasRunningTime: false, hasRecalculateTime: false, hasUpdateTime: false },
      { status: 'RECALCULATING', hasRunningTime: false, hasRecalculateTime: false, hasUpdateTime: false }
    ];

    scenarios.forEach(({ status, hasRunningTime, hasRecalculateTime, hasUpdateTime }) => {
      const data: any = { id: 1, status };
      
      if (hasRunningTime) data.runningTime = '2024-01-01 10:00:00';
      if (hasRecalculateTime) data.recalculateRunningTime = '2024-01-01 11:00:00';
      if (hasUpdateTime) data.updateTime = '2024-01-01 09:00:00';

      const realTimeResult = getRealTimeRunning(data);
      const recalculateTimeResult = getRecalculateRunning(data);

      if (status === 'RUNNING' && hasUpdateTime) {
        expect(realTimeResult).toBe('格式化时间: 2024-01-01 09:00:00');
      } else if (status === 'RUNNING') {
        expect(realTimeResult).toBe('--');
      } else {
        expect(realTimeResult).toBe('--');
      }

      if (status === 'RECALCULATING' && hasUpdateTime) {
        expect(recalculateTimeResult).toBe('格式化时间: 2024-01-01 09:00:00');
      } else if (status === 'RECALCULATING') {
        expect(recalculateTimeResult).toBe('--');
      } else {
        expect(recalculateTimeResult).toBe('--');
      }
    });
  });
})
