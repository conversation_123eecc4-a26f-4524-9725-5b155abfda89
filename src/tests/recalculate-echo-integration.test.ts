import { describe, it, expect, beforeEach, vi } from 'vitest'

// 模拟 localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(globalThis, 'localStorage', {
  value: localStorageMock
})

// 模拟数据结构
interface VariableParam {
  name: string;
  label: string;
  value: string;
}

interface ProcessTableParam {
  id: string | number;
  jobId?: string;
  jobName: string;
  lastRecalculateStartTime?: string;
  lastRecalculateVariables?: VariableParam[];
}

// 模拟完整的重算流程
class RecalculateFlow {
  // 模拟重算点击事件
  static recalculateClick(row: ProcessTableParam): ProcessTableParam {
    const savedParams = localStorage.getItem(`recalculate_params_${row.id}`);
    if (savedParams) {
      try {
        const params = JSON.parse(savedParams);
        row.lastRecalculateStartTime = params.startTime;
        row.lastRecalculateVariables = params.variables;
      } catch (error) {
        console.warn('解析重算参数失败:', error);
      }
    }
    return row;
  }

  // 模拟重算确认事件
  static handleRecalculateConfirm(data: any): any {
    const recalculateParams = {
      startTime: data.startTime,
      variables: data.variables || []
    };
    localStorage.setItem(`recalculate_params_${data.id}`, JSON.stringify(recalculateParams));
    return recalculateParams;
  }

  // 模拟初始化表单
  static initForm(rowData?: ProcessTableParam) {
    const today = '2024-01-15'
    
    const hasLastRecalculateData = rowData?.lastRecalculateStartTime || rowData?.lastRecalculateVariables?.length

    const form = {
      startTime: hasLastRecalculateData ? (rowData?.lastRecalculateStartTime || '') : '',
      endTime: today
    }

    let variables: VariableParam[] = []
    if (hasLastRecalculateData && rowData?.lastRecalculateVariables?.length) {
      variables = [...rowData.lastRecalculateVariables]
    }

    return { form, variables }
  }
}

describe('重算设置回显功能集成测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该完整地处理重算参数的保存和回显流程', () => {
    // 第一步：用户提交重算参数
    const submitData = {
      id: 'test8',
      startTime: '2024-01-10',
      endTime: '2024-01-15',
      variables: [
        { name: '租户id', label: 'tenantId', value: '1' },
        { name: '用户id', label: 'userId', value: '123' }
      ]
    }

    // 保存参数到本地存储
    RecalculateFlow.handleRecalculateConfirm(submitData)

    // 验证保存操作
    expect(localStorage.setItem).toHaveBeenCalledWith(
      'recalculate_params_test8',
      JSON.stringify({
        startTime: '2024-01-10',
        variables: [
          { name: '租户id', label: 'tenantId', value: '1' },
          { name: '用户id', label: 'userId', value: '123' }
        ]
      })
    )

    // 第二步：模拟用户再次点击重算按钮
    const mockSavedData = JSON.stringify({
      startTime: '2024-01-10',
      variables: [
        { name: '租户id', label: 'tenantId', value: '1' },
        { name: '用户id', label: 'userId', value: '123' }
      ]
    })

    localStorageMock.getItem.mockReturnValue(mockSavedData)

    const originalRow: ProcessTableParam = {
      id: 'test8',
      jobName: 'test_job'
    }

    // 加载历史参数
    const updatedRow = RecalculateFlow.recalculateClick(originalRow)

    // 验证加载操作
    expect(localStorage.getItem).toHaveBeenCalledWith('recalculate_params_test8')
    expect(updatedRow.lastRecalculateStartTime).toBe('2024-01-10')
    expect(updatedRow.lastRecalculateVariables).toEqual([
      { name: '租户id', label: 'tenantId', value: '1' },
      { name: '用户id', label: 'userId', value: '123' }
    ])

    // 第三步：初始化表单，应该回显历史数据
    const { form, variables } = RecalculateFlow.initForm(updatedRow)

    expect(form.startTime).toBe('2024-01-10')
    expect(form.endTime).toBe('2024-01-15')
    expect(variables).toHaveLength(2)
    expect(variables[0]).toEqual({ name: '租户id', label: 'tenantId', value: '1' })
    expect(variables[1]).toEqual({ name: '用户id', label: 'userId', value: '123' })
  })

  it('应该处理首次使用（无历史数据）的情况', () => {
    localStorageMock.getItem.mockReturnValue(null)

    const originalRow: ProcessTableParam = {
      id: 'test_new',
      jobName: 'new_job'
    }

    // 尝试加载历史参数（应该没有）
    const updatedRow = RecalculateFlow.recalculateClick(originalRow)

    expect(localStorage.getItem).toHaveBeenCalledWith('recalculate_params_test_new')
    expect(updatedRow.lastRecalculateStartTime).toBeUndefined()
    expect(updatedRow.lastRecalculateVariables).toBeUndefined()

    // 初始化表单，应该使用默认值
    const { form, variables } = RecalculateFlow.initForm(updatedRow)

    expect(form.startTime).toBe('')
    expect(form.endTime).toBe('2024-01-15')
    expect(variables).toHaveLength(0)
  })

  it('应该处理部分历史数据的情况', () => {
    // 只有开始时间，没有变量
    const mockSavedData = JSON.stringify({
      startTime: '2024-01-08',
      variables: []
    })

    localStorageMock.getItem.mockReturnValue(mockSavedData)

    const originalRow: ProcessTableParam = {
      id: 'test_partial',
      jobName: 'partial_job'
    }

    const updatedRow = RecalculateFlow.recalculateClick(originalRow)
    const { form, variables } = RecalculateFlow.initForm(updatedRow)

    expect(form.startTime).toBe('2024-01-08')
    expect(form.endTime).toBe('2024-01-15')
    expect(variables).toHaveLength(0)
  })

  it('应该处理数据损坏的情况', () => {
    localStorageMock.getItem.mockReturnValue('invalid json data')

    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

    const originalRow: ProcessTableParam = {
      id: 'test_corrupted',
      jobName: 'corrupted_job'
    }

    const updatedRow = RecalculateFlow.recalculateClick(originalRow)

    expect(consoleSpy).toHaveBeenCalledWith('解析重算参数失败:', expect.any(Error))
    expect(updatedRow.lastRecalculateStartTime).toBeUndefined()
    expect(updatedRow.lastRecalculateVariables).toBeUndefined()

    // 初始化表单，应该使用默认值
    const { form, variables } = RecalculateFlow.initForm(updatedRow)

    expect(form.startTime).toBe('')
    expect(form.endTime).toBe('2024-01-15')
    expect(variables).toHaveLength(0)

    consoleSpy.mockRestore()
  })
})