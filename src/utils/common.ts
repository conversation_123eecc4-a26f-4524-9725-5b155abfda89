import type { FormInstance } from "element-plus";

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

import { Ref } from "vue";

import { type AxiosResponse } from "axios";

// 单个表单校验
export async function getFormRules(
  ruleFormRef: FormInstance | undefined,
  type: string | undefined,
  data: EditDrawerParam | AllInputParam | AllOutputParam,
  name: string
) {
  let childRuleBool = false;
  if (!ruleFormRef) return false;
  try {
    const valid = await ruleFormRef.validate();
    if (valid) {
      flowChartStore.updateTempData(type, data);
      childRuleBool = true;
    }
  } catch (error) {
    ElMessage.error(`${name}页面存在表单校验未通过，请检查表单内容`);
  }
  return childRuleBool;
}

// 多个表单校验
export async function getFormRulesAll(
  childrenRef: Ref<ChildrenComponentParam[]>
) {
  let ruleBool = true;
  if (!childrenRef.value) return false;
  for (let item of childrenRef.value) {
    if (!isFunction(item.getNewData)) continue;
    const result = await item.getNewData();
    if (!result) {
      ruleBool = false;
      break;
    }
  }
  return ruleBool;
}

// 检查该字段是否需要更新
export async function checkAllFieldUpdate(
  childrenRef: Ref<ChildrenComponentParam[]>
) {
  let ruleBool = false;
  if (!childrenRef.value) return false;
  for (let item of childrenRef.value) {
    if (!isFunction(item.checkFieldUpdate)) continue;
    const result = await item.checkFieldUpdate();
    if (result) {
      ruleBool = true;
      break;
    }
  }
  return ruleBool;
}

// 获取连接的具体数据来进行字段的获取
export function getNodeFrom(locatedNodeOptions: ComputerOptionsParam[]) {
  const froms: string[] = [];
  if (locatedNodeOptions.length == 0) return froms;
  locatedNodeOptions.forEach((item) => {
    froms.push(item.value);
  });
  return froms;
}

// 获取当前节点的所有入边的字段重新定义
export function getResetField(incomers: GraphNode[]) {
  let resetField: ComputerOptionsParam[] = [];
  resetField = incomers.map((item) => {
    const {
      id,
      data: { name, type, dataStream, nodeType },
    } = item;
    return {
      value: id,
      label: name + "（" + id + "）",
      key: type,
      dataStream,
      nodeType,
    };
  });
  return resetField;
}

// 获取通用枚举字段自定义
export function getCommonEnumField(data: FieldTableParam[]) {
  let commonEnumField: NodeOptionsParam[] = [];
  commonEnumField = data.map((item) => {
    const { field, description, frontedType } = item;
    return {
      value: field,
      label: description + "（" + field + "）",
      key: frontedType,
    };
  });
  return commonEnumField;
}

// 流程图设计页面--各个算子的最终结果字段对应方法字段
export const nodeTypeFieldMapFun: NodeTypeFieldMapParam = {
  DatalogDataStreamSource: inputFieldResult,
  CfgSource: inputFieldResult,
  ModelDataStreamSource: modelInputFieldResult,
  StreamJoinFunction: joinFieldResult,
  StreamGroupByFunction: groupFieldResult,
  GeneralFilterFunction: filterFieldResult,
  FieldSettingFunction: filterFieldResult,
  ExceptionHandlingFunction: exceptionFieldResult,
  CommonRedisSink: filterFieldResult,
  CommonJdbcSink: filterFieldResult,
};

/**
 * @description: 各个算子的最终结果字段对应方法字段
 * @param {string} type
 * @param {*} data
 * @return {*}
 */
export async function nodeTypeFieldMap(
  type: string,
  data?: EditDrawerParam | AllInputParam | AllOutputParam
) {
  return await nodeTypeFieldMapFun[type](data);
}

// 获取连接的具体数据来进行字段的获取
export async function getConnectData(
  locatedNodeOptions: ComputerOptionsParam[]
) {
  if (locatedNodeOptions.length == 0) return ElMessage.error("请先连接数据源");
  const nodeData = locatedNodeOptions[0];
  const data =
    nodeData?.nodeType == "source"
      ? flowChartStore.inputOperatorData
      : flowChartStore.computerOperatorData;
  let filterData = data.find((item) => item.id === nodeData.value) as
    | AllInputParam
    | EditDrawerParam;
  if (!filterData || !filterData.type) {
    return ElMessage.error("连接数据源的字段暂未配置");
  }
  // 对定时记录进行专门处理
  if (
    nodeData?.key === "DatalogDataStreamSource" &&
    !(filterData as AllInputParam)?.latenessDay
  ) {
    return ElMessage.error("连接数据源的字段暂未配置");
  }
  const type = filterData.type;
  // 调用不同算子的不同方法获取字段
  const getTableData = await nodeTypeFieldMap(type, filterData);
  return { getTableData, filterData };
}

// 接口导入
import customApi from "@/api/custom";

/**
 * @description: 定时记录和业务维度配置算子的最终字段结果
 * @param {AllInputParam} data
 * @return {*}
 */
export async function inputFieldResult(data: AllInputParam) {
  if (!data.type) return [];

  if (data.type === "CfgSource") {
    return cloneDeep(flowChartStore.inputSource[data.id]?.fields || []);
  }

  try {
    // 查询字段表数据
    const res = await customApi.queryFieldType({ functionType: data.type });
    return cloneDeep(res.data) || [];
  } catch (error) {
    console.log(error);
    return [];
  }
}

/**
 * @description: 模型数据输入算子的最终字段结果
 * @param {AllInputParam} data
 * @return {*}
 */
export async function modelInputFieldResult(data: AllInputParam) {
  let table: FieldTableParam[] = [];
  if (!data.syncModel) return table;

  try {
    // 查询字段表数据
    const res = await customApi.queryModelFields({
      modelLabel: data.syncModel,
    });
    table = res.data;
  } catch (err) {
    console.log(err);
  }
  return table;
}

// join连接算子的最终字段结果
export async function joinFieldResult(data: EditDrawerParam) {
  let table: FieldTableParam[] = [];
  const param = {
    id: data?.id,
    associationCondition: data?.joinCondition || "",
    joinMode: data?.joinType || "",
    streamA: data?.streamA || "",
    streamB: data?.streamB || "",
    tableA: data?.tableA || [],
    tableB: data?.tableB || [],
  };
  // 查询字段表数据
  const res = await customApi.queryJoinModelsData(param);
  if (res.code === 0) {
    table = cloneDeep(res.data);
  }
  return table;
}

// 分组汇总算子的最终字段结果
export async function groupFieldResult(data: EditDrawerParam) {
  let table: FieldTableParam[] = [];
  const param = {
    id: data?.id,
    // func: data?.func || "",
    functions: data?.functions,
    funcField: data?.funcField,
    groupBy: data?.groupBy || [],
    joinTable: data?.joinTable || [],
    formulas: data?.formulas,
    // periodField: data?.periodField,
    periodEdges: data?.periodEdges,
  };
  // 查询字段表数据
  const res = await customApi.queryGroupModelsData(param);
  if (res.code === 0) {
    table = cloneDeep(res.data);
  }
  return table;
}

/**
 * @description: 异常处理算子的处理后字段结果
 * @param {EditDrawerParam} data
 * @return {*}
 */
export async function exceptionFieldResult(data: EditDrawerParam) {
  let table: FieldTableParam[] = [];
  const param = {
    id: data?.id,
    businessScene: data?.businessScene || "",
    dynamicField: data?.dynamicField || {},
    upstreamFieldTable: data?.upstreamFieldTable || [],
  };
  try {
    // 查询字段表数据
    const res = await customApi.queryProcessedFields(param);
    table = cloneDeep(res.data);
  } catch (err) {
    console.log(err);
  }

  return table;
}

// 筛选算子的最终字段结果
export async function filterFieldResult(
  data: EditDrawerParam | AllInputParam | AllOutputParam
) {
  const filterTable = cloneDeep(data.filterTable || []);
  return filterTable;
}

// 删除节点时，需要清除的节点字段
export const nodeFieldClearMap: NodeClearMapParam = {
  StreamJoinFunction: [
    "from",
    "dataStream",
    "associationTableData",
    "joinCondition",
    "streamA",
    "streamAM",
    "streamB",
    "streamBM",
    "tableA",
    "tableB",
    "joinType",
  ],
  StreamGroupByFunction: [
    "from",
    "dataStream",
    "funcField",
    "groupBy",
    "joinTable",
    "periodField",
    "periodEdges",
    "functions",
    "formulas",
  ],
  GeneralFilterFunction: [
    "from",
    "dataStream",
    "filterCondition",
    "filterConditionArray",
    "filterTable",
  ],
  FieldSettingFunction: [
    "from",
    "dataStream",
    "fieldTable",
    "allfieldTable",
    "filterTable",
  ],
  // 异常处理算子
  ExceptionHandlingFunction: [
    "from",
    "dataStream",
    "dynamicField",
    "upstreamFieldTable",
  ],
  // 通用redis数据源字段删除，对应redisSinkSetting页面
  //CommonRedisSink: ["from", "dataStream", "rediskey"],
  // 业务定制redis数据源字段删除，对应redisBusinessSetting页面
  CommonRedisSink: [
    "from",
    "dataStream",
    "modelAssociation",
    "syncModel",
    "redisTableName",
  ],
  CommonJdbcSink: [
    "from",
    "dataStream",
    "modelAssociation",
    "syncModel",
    "syncModelLabel",
  ],
};

import type { Node, Edge, GraphNode } from "@vue-flow/core";

// 广度优先搜索算法获取当前删除节点的所有关联的子节点算子
export function clearChildrenNode(
  id: string,
  nodes: Node[],
  edges: Edge[],
  deleteType: "deleteNode" | "deleteEdge" | "fieldChange"
) {
  const list = [id];
  // 用于判断后续的节点是否已经遍历过
  const visited = new Set(list);
  const allChildrenNode: Node[] = [];
  while (list.length > 0) {
    const nodeId = list.shift();
    const currentNode = nodes.find((item) => item.id === nodeId);
    if (!currentNode) continue;
    allChildrenNode.push(currentNode);
    edges.forEach((item) => {
      if (item.source === nodeId && !visited.has(item.target)) {
        list.push(item.target);
        visited.add(item.target);
      }
    });
  }
  if (deleteType === "fieldChange") {
    allChildrenNode.shift();
  }
  // 更新最新的节点删除字段确定
  flowChartStore.getDeleteNodeFiled(nodeFieldClearMap);
  // 所有数据都进行清除
  allChildrenNode.forEach((item: Node, index: number) => {
    // 判断是否是删除操作同时满足是首删除节点才清空所有数据，其他的只删除与字段变化相关的字段
    const isFirst = deleteType === "deleteNode" && index === 0;
    flowChartStore.deleteData(item.data.nodeType, item.id, isFirst);
  });
}

// 导入过滤方法模型定义
import { filterMethodKeys } from "@/utils/globalField";

// 筛选算子中的各种过滤条件组合成字符串文本
export function getAllTitle(
  title: string,
  item: FilterConditionArrayParam,
  fieldType?: string
) {
  switch (item.selectFunctionName) {
    case "contains":
      return `${title} string.contains(${item.selectField},'${item.inputValue}')`;
    case "notContains":
      return `${title} !string.contains(${item.selectField},'${item.inputValue}')`;
    case "range":
      return getRangeTitle(title, item);
    case "in":
      return getEnumTitle(title, item, "in", fieldType);
    case "notIn":
      return getEnumTitle(title, item, "notIn", fieldType);
    default:
      const keyType = filterMethodKeys[item.selectFunctionName];
      // 判断是否添加了null或者notNull
      const bool = ["null", "notNull"].includes(item.selectFunctionName);
      return bool
        ? `${title} ${item.selectField} ${keyType}`
        : `${title} ${item.selectField} ${keyType} ${item.inputValue}`;
  }
}

function getRangeTitle(title: string, item: FilterConditionArrayParam) {
  if (!Array.isArray(item.inputValue)) return "";
  switch (item.rangeType) {
    case "lcrc":
      return `${title} (${item.inputValue[0]} <= ${item.selectField} && ${item.selectField} <= ${item.inputValue[1]})`;
    case "lcro":
      return `${title} (${item.inputValue[0]} <= ${item.selectField} && ${item.selectField} < ${item.inputValue[1]})`;
    case "lorc":
      return `${title} (${item.inputValue[0]} < ${item.selectField} && ${item.selectField} <= ${item.inputValue[1]})`;
    case "loro":
      return `${title} (${item.inputValue[0]} < ${item.selectField} && ${item.selectField} < ${item.inputValue[1]})`;
  }
}

function getEnumTitle(
  title: string,
  item: FilterConditionArrayParam,
  type: string,
  fieldType?: string
) {
  if (!Array.isArray(item.inputValue)) return "";
  const keySym = type == "in" ? "include" : "!include";
  const bool = fieldType === "enum" && typeof item.inputValue[0] === "number";
  if (fieldType === "number" || bool) {
    return `${title} ${keySym}([${item.inputValue}], ${item.selectField})`;
  } else {
    const inputValueString = item.inputValue.map((v) => `'${v}'`).join(",");
    return `${title} ${keySym}([${inputValueString}], ${item.selectField})`;
  }
}

export function getExportJSON(res: AxiosResponse) {
  const url = window.URL.createObjectURL(new Blob([JSON.stringify(res.data)]));
  const link = document.createElement("a");
  link.style.display = "none";
  link.href = url;
  // 获取导出文件的名称
  const fileNameStr = res.headers["content-disposition"];
  var reg = /filename=(.*)/;
  var arr = reg.exec(fileNameStr);
  var fileName = "导出文件";
  if (arr && arr[1].trim()) {
    if (arr[1].trim().indexOf('"') !== -1) {
      fileName = decodeURIComponent(arr[1].trim().slice(1, arr[1].length - 1));
    } else {
      fileName = decodeURIComponent(arr[1].trim());
    }
  }
  fileName = checkFileName(fileName);
  link.setAttribute("download", fileName);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// 檢查导出名称，没有后缀的话默认补上json
function checkFileName(name: string) {
  if (name.includes(".")) {
    return name;
  }
  return name + ".json";
}

export function getCopyData(text: string) {
  let textarea = document.createElement("textarea");
  textarea.value = text;
  document.body.appendChild(textarea);
  textarea.select();
  if (document.execCommand("copy")) {
    document.execCommand("copy");
    ElMessage.success("复制成功");
  }
  document.body.removeChild(textarea);
}

import moment from "moment";
// 引入lodash方法
import { isFunction } from "lodash-es";

export const common = {
  // 表格内容格式化处理
  formatText() {
    return function (row: any, column: any, cellValue: any, index: number) {
      if (["", undefined, null, NaN].includes(cellValue)) {
        return "--";
      } else {
        return cellValue;
      }
    };
  },

  // 格式化处理表格内时间数据展示
  formatterTime() {
    return function (row: any, column: any, cellValue: any) {
      // 检查cellValue是否为字符串，并且是否符合时间戳的格式
      if (typeof cellValue === "string") {
        // 将字符串类型的时间戳转换为数值类型
        cellValue = parseInt(cellValue);
      }
      return cellValue ? moment(cellValue).format("YYYY-MM-DD HH:mm:ss") : "--";
    };
  },

  // 文本内容格式化
  formatTextContent(cellValue: any) {
    if (["", undefined, null, NaN].includes(cellValue)) {
      return "--";
    } else {
      return cellValue;
    }
  },

  // 时间数据格式化
  formatTime(cellValue: any) {
    return cellValue ? moment(cellValue).format("YYYY-MM-DD HH:mm:ss") : "--";
  },

  // 表格中输入数字控制
  handleNum(value: string | number, num: number) {
    var reg = new RegExp("^(\\-)*(\\d+)\\.(\\d{0," + num + "}).*$");
    var reg2 = new RegExp("^(\\d{0,11})(\\d+)\\.(\\d{0," + num + "}).*$");
    var reg3 = new RegExp("^(\\d{0,11})(\\d+)$");
    var val: string | number = String(value);
    if (val) {
      val = val.replace(/[^\d.-]/g, ""); //清除数字和'.'以外的字符
      val = val.replace(/-+/g, "-"); //清除多余的'-'
      val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", "."); //只保留顺位第一的'.'
      val = val.replace(reg, "$1$2.$3"); //只能输入两位位小数
    }
    if (val.startsWith("-") && !isNaN(Number(val.substring(1)))) {
      // 如果字符串以'-'开头，并且'-'后面跟着的是数字
      val = val.replace(/^-\./g, "-0."); // 如果是负数且以'-.'开头，补上'0'
    } else if (val.startsWith("-") && val.length > 1) {
      // 如果字符串以'-'开头，且长度大于1
      val = val.replace("-", ""); // 移除负号，稍后再加回来
      let decimalPart = val.match(/\.(.*)/); // 获取小数部分
      if (decimalPart) {
        val = val.replace(decimalPart[0], "0" + decimalPart[0]); // 在小数点前补上'0'
      }
      val = "-" + val; // 将负号加回来
    } else if (val.startsWith("-") && val.length === 1) {
      // 如果字符串只包含一个负号
      val = ""; // 清空字符串，因为这是无效的输入
    }
    if (val.length >= 2 && val.indexOf(".") !== 1 && Number(val[0]) == 0) {
      //在非小数的时候清除前导0
      val = val.replace(/0/, "");
    }
    if (val.length >= 2 && val.indexOf(".") === 0) {
      // 在先输入小数点时补0
      val = Number(val);
    }
    val = String(val);
    let isMinus = false; // 是否为负数
    if (val.startsWith("-")) {
      isMinus = true;
      val = val.replace("-", "");
    }
    if (String(val).indexOf(".") !== -1 && Number(val) > 99999999999.99) {
      val = val.replace(reg2, "$1.$3");
    } else if (String(val).indexOf(".") === -1 && String(val).length > 11) {
      val = val.replace(reg3, "$1");
    }
    return isMinus ? "-" + val : val;
  },

  // 表格内部错误信息格式化为空白文本
  formatEmptyText() {
    return function (row: any, column: any, cellValue: any, index: number) {
      if (["", undefined, null, NaN].includes(cellValue)) {
        return "";
      } else {
        return cellValue;
      }
    };
  },
};
