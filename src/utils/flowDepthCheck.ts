import type { Edge, Node } from "@vue-flow/core";

export const flowDepthCheck = (
  nodes: Node[],
  edges: Edge[],
  maxDepth: number,
  isRunning?: boolean
) => {
  // 获取所有path路径信息
  const paths: string[][] = [];

  // 构建图结构
  const graph: Record<string, string[]> = {};

  edges.forEach((edge) => {
    graph[edge.source] ||= [];
    graph[edge.source].push(edge.target);
  });

  let exist = false; // 判断是否存在超过5层
  // 从每个节点开始搜索
  for (const node in graph) {
    if (dfs(node, maxDepth, [node], 1)) {
      exist = true;
      break; // 找到超过5层的路径，立即停止循环
    }
  }

  // 深度优先搜索函数
  function dfs(
    node: string,
    maxDepth: number = 5,
    path: string[] = [],
    depth: number = 1
  ) {
    if (depth > 1) {
      paths.push(path);
    }
    if (depth >= maxDepth) {
      console.log(`存在深度超过${maxDepth}的路径：${path.join(" -> ")}`);
      ElMessage.error(`流程图深度超过${maxDepth}层，请重新设计`);
      return true;
    }

    if (!graph[node]) {
      return false;
    }

    for (const nextNode of graph[node]) {
      if (path.includes(nextNode)) {
        continue; // 避免循环引用
      }
      const newPath = [...path, nextNode];
      if (dfs(nextNode, maxDepth, newPath, depth + 1)) {
        return true;
      }
    }
    return false;
  }

  if (!exist && isRunning) {
    pathCheck();
  }

  // 判断所有情况的路径是否存在一个满足情况
  function pathCheck() {
    for (let item of paths) {
      if (nodeTypeCheck(item)) {
        exist = false;
        return; // 一旦找到满足条件的路径，就终止循环
      }
    }
    exist = true;
    ElMessage.error(`流程图中缺少输入或者输出算子，请重新设计`);
  }

  // 判断路径中是否存在source和sink
  function nodeTypeCheck(path: string[]) {
    let isStart = false;
    let isEnd = false;
    path.forEach((item) => {
      const node = nodes.find((node) => node.id === item)?.data;
      if (node?.nodeType === "source") {
        isStart = true;
      }
      if (node?.nodeType === "sink") {
        isEnd = true;
      }
    });
    return isStart && isEnd;
  }

  return exist;
};
