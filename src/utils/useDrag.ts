import { useVueFlow } from "@vue-flow/core";
import type { Node } from "@vue-flow/core";

const uid: Record<string, number> = {};

function getUid(id: string) {
  if (!uid[id]) {
    uid[id] = 1;
  }
  return String(uid[id]++);
}

/**
 * @description: 获取当前独一无二的id
 * @return {*}
 */
function getStringId(id: string) {
  return `T${id}`;
}

const state = {
  draggedType: ref<string>(""),
  draggedLabel: ref<string>(""),
  draggedComputeType: ref<string>(""),
  isDragOver: ref(false),
  isDragging: ref(false),
  flowId: ref<string>(""),
  stream: ref<string>(""),
};

export default function useDragAndDrop() {
  const {
    draggedType,
    draggedComputeType,
    draggedLabel,
    isDragOver,
    isDragging,
    flowId,
    stream,
  } = state;

  const { addNodes, screenToFlowCoordinate, onNodesInitialized, updateNode } =
    useVueFlow();

  watch(isDragging, (dragging) => {
    document.body.style.userSelect = dragging ? "none" : "";
  });

  /**
   * @description:用户开始拖动元素时触发
   * @param {DragEvent} event
   * @param {string} type
   * @return {*}
   */
  function onDragStart(
    event: DragEvent,
    type: string,
    computeType: string,
    label: string,
    id: string,
    dataStream?: string
  ) {
    if (event.dataTransfer) {
      event.dataTransfer.setData("application/vueflow", type);
      event.dataTransfer.effectAllowed = "move";
    }

    draggedLabel.value = label;
    draggedType.value = type;
    draggedComputeType.value = computeType;
    isDragging.value = true;
    flowId.value = id;
    if (dataStream) {
      stream.value = dataStream;
    }

    document.addEventListener("drop", onDragEnd);
  }

  /**
   * @description: 当某被拖动的对象在另一对象容器范围内拖动时触发此事件
   * @param {DragEvent} event
   * @return {*}
   */
  function onDragOver(event: DragEvent) {
    event.preventDefault();

    if (draggedType.value) {
      isDragOver.value = true;

      if (event.dataTransfer) {
        event.dataTransfer.dropEffect = "move";
      }
    }
  }

  function onDragLeave() {
    isDragOver.value = false;
  }

  /**
   * @description: 用户完成元素拖动后触发
   * @return {*}
   */
  function onDragEnd() {
    isDragging.value = false;
    isDragOver.value = false;
    draggedType.value = "";
    draggedComputeType.value = "";
    draggedLabel.value = "";
    flowId.value = "";
    stream.value = "";
    document.removeEventListener("drop", onDragEnd);
  }

  /**
   * @description: 元素正在拖动时触发
   * @param {DragEvent} event
   * @return {*}
   */
  function onDrop(event: DragEvent) {
    // 禁止除左侧面板外的拖拽
    if (
      event.dataTransfer &&
      (event.dataTransfer.effectAllowed !== "move" ||
        event.dataTransfer.types[0] !== "application/vueflow")
    ) {
      return false;
    }
    // 获取拖动过程中鼠标在画布中的坐标
    const position = screenToFlowCoordinate({
      x: event.clientX,
      y: event.clientY,
    });

    const nodeId2 = getUid(flowId.value);

    // 特殊规则标识
    const nodeId = getStringId(nodeId2);

    const newNode: NewNode = {
      id: nodeId,
      type: "default",
      position,
      data: {
        name: draggedLabel.value,
        nodeType: draggedType.value,
        type: draggedComputeType.value,
        dataStream: stream.value,
      },
    };
    const { off } = onNodesInitialized(() => {
      updateNode(nodeId, (node) => ({
        position: {
          x: node.position.x - node.dimensions.width / 2,
          y: node.position.y - node.dimensions.height / 2,
        },
      }));

      off();
    });

    // 节点的添加
    addNodes(newNode);
  }

  /**
   * @description: 获取当前任务的最新uid
   * @param {Node[]} nodes
   * @param {string | number} id
   * @return {*}
   */
  function getCurrentUid(nodes: Node[], id: string | number) {
    const arr: number[] = [];
    if (!nodes || nodes.length === 0) {
      uid[id] = 1;
      return;
    }
    nodes.forEach((item: Node) => {
      arr.push(getNumberUid(item.id));
    });
    uid[id] = Math.max(...arr) + 1;
  }

  function getNumberUid(id: string) {
    const parts = id.split(/T/);
    return parts[1] ? parseInt(parts[1], 10) : 0;
  }

  return {
    draggedType,
    isDragOver,
    isDragging,
    onDragStart,
    onDragLeave,
    onDragOver,
    onDrop,
    getCurrentUid,
  };
}
