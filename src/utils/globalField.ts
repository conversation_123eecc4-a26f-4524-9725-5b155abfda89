// 分组汇总算子 -- 基本配置
import groupSumSetting from "@/views/flowChart/components/CustomDrawer/groupSumDrawer/groupSumSetting.vue";

// 分组汇总算子 -- 合并后字段
import groupFieldPreview from "@/views/flowChart/components/CustomDrawer/groupSumDrawer/groupFieldPreview.vue";

// 分组汇总算子 -- 预览页面
import groupDataPreview from "@/views/flowChart/components/CustomDrawer/groupSumDrawer/groupDataPreview.vue";

// 定时记录输入算子 -- 基本配置
import timeRecordSetting from "@/views/flowChart/components/CustomDrawer/groupSumDrawer/timeRecordDrawer/timeRecordSetting.vue";

// 定时记录输入算子 -- 调试预览
import timeDataPreview from "@/views/flowChart/components/CustomDrawer/groupSumDrawer/timeRecordDrawer/timeDataPreview.vue";

// 业务维度配置输入算子  -- 基本配置
import businessConfigSetting from "@/views/flowChart/components/CustomDrawer/businessConfigDrawer/businessConfigSetting.vue";

// 业务维度配置输入算子  -- 调试预览
import businessDataPreview from "@/views/flowChart/components/CustomDrawer/businessConfigDrawer/businessDataPreview.vue";

// join连接算子 -- 基本配置
import joinOperatorSetting from "@/views/flowChart/components/CustomDrawer/joinOperatorDrawer/joinOperatorSetting.vue";

// join连接算子 -- 连接字段预览
import joinFieldPreview from "@/views/flowChart/components/CustomDrawer/joinOperatorDrawer/joinFieldPreview.vue";

// join连接算子 -- 预览页面
import joinDataPreview from "@/views/flowChart/components/CustomDrawer/joinOperatorDrawer/joinDataPreview.vue";

// 筛选算子 -- 基本配置
import filterOperatorSetting from "@/views/flowChart/components/CustomDrawer/filterOperatorDrawer/filterOperatorSetting.vue";

// 筛选算子 -- 预览页面
import filterDataPreview from "@/views/flowChart/components/CustomDrawer/filterOperatorDrawer/filterDataPreview.vue";

// 字段设置算子 -- 基本配置
import calcFieldSetting from "@/views/flowChart/components/CustomDrawer/fieldSettingDrawer/calcFieldSetting.vue";

// 字段设置算子 -- 预览页面
import calcDataPreview from "@/views/flowChart/components/CustomDrawer/fieldSettingDrawer/calcDataPreview.vue";

// redis输出算子 -- 基本配置
import redisSinkSetting from "@/views/flowChart/components/CustomDrawer/redisSinkDrawer/redisSinkSetting.vue";

// redis输出算子 -- 对业务定制页面输出
import redisBusinessSetting from "@/views/flowChart/components/CustomDrawer/redisSinkDrawer/redisBusinessSetting.vue";

// redis输出算子 -- 预览页面
import redisDataPreview from "@/views/flowChart/components/CustomDrawer/redisSinkDrawer/redisDataPreview.vue";

// 模型输出算子 -- 基本配置
import modelOutputSetting from "@/views/flowChart/components/CustomDrawer/modelOutputDrawer/modelOutputSetting.vue";

// 模型输出算子 -- 预览页面
import modelDataPreview from "@/views/flowChart/components/CustomDrawer/modelOutputDrawer/modelDataPreview.vue";

// 异常处理算子 -- 基本配置
import exceptionHandlerSetting from "@/views/flowChart/components/CustomDrawer/exceptionHandlerDrawer/exceptionHandlerSetting.vue";

// 异常处理算子 -- 处理后字段
import exceptionHandlerField from "@/views/flowChart/components/CustomDrawer/exceptionHandlerDrawer/exceptionHandlerField.vue";

// 异常处理算子 -- 预览页面
import exceptionDataPreview from "@/views/flowChart/components/CustomDrawer/exceptionHandlerDrawer/exceptionDataPreview.vue";

// 模型输入算子 -- 基本配置
import modelInputSetting from "@/views/flowChart/components/CustomDrawer/modelInputDrawer/modelInputSetting.vue";

// 模型输入算子 -- 调试预览
import modelInputDataPreview from "@/views/flowChart/components/CustomDrawer/modelInputDrawer/modelInputDataPreview.vue";

// 流程图设计页面--抽屉中菜单参数
// 内部的type类型名称是后端定义的节点类型
export const drawerMenuList: MenuParam[] = [
  {
    // 节点类型：定时记录输入节点
    type: "DatalogDataStreamSource",
    subMenu: [
      {
        label: "基本配置",
        key: "timeRecordSetting",
        type: "MenuItem",
        icon: "basicConfig",
        component: timeRecordSetting,
      },
      {
        label: "调试预览",
        key: "timeDataPreview",
        type: "MenuItem",
        icon: "debugPreview",
        component: timeDataPreview,
      },
    ],
  },
  {
    // 节点类型：业务维度配置输入节点
    type: "CfgSource",
    subMenu: [
      {
        label: "基本配置",
        key: "businessConfigSetting",
        type: "MenuItem",
        icon: "basicConfig",
        component: businessConfigSetting,
      },
      {
        label: "调试预览",
        key: "businessDataPreview",
        type: "MenuItem",
        icon: "debugPreview",
        component: businessDataPreview,
      },
    ],
  },
  {
    // 节点类型：模型数据输入算子
    type: "ModelDataStreamSource",
    subMenu: [
      {
        label: "基本配置",
        key: "modelInputSetting",
        type: "MenuItem",
        icon: "basicConfig",
        component: modelInputSetting,
      },
      {
        label: "调试预览",
        key: "modelInputDataPreview",
        type: "MenuItem",
        icon: "debugPreview",
        component: modelInputDataPreview,
      },
    ],
  },
  {
    // 节点类型：join连接节点
    type: "StreamJoinFunction",
    subMenu: [
      {
        label: "基本配置",
        key: "joinOperatorSetting",
        type: "MenuItem",
        icon: "basicConfig",
        component: joinOperatorSetting,
      },
      {
        label: "合并后字段",
        key: "joinFieldPreview",
        type: "MenuItem",
        icon: "mergedField",
        component: joinFieldPreview,
      },
      {
        label: "数据预览",
        key: "joinDataPreview",
        type: "MenuItem",
        icon: "dataPreview",
        component: joinDataPreview,
      },
    ],
  },
  {
    // 节点类型：分组汇总节点
    type: "StreamGroupByFunction",
    subMenu: [
      {
        label: "基本配置",
        key: "groupSumSetting",
        type: "MenuItem",
        icon: "basicConfig",
        component: groupSumSetting,
      },
      {
        label: "汇总后字段",
        key: "groupFieldPreview",
        type: "MenuItem",
        icon: "mergedField",
        component: groupFieldPreview,
      },
      {
        label: "数据预览",
        key: "groupDataPreview",
        type: "MenuItem",
        icon: "dataPreview",
        component: groupDataPreview,
      },
    ],
  },
  {
    // 节点类型：筛选算子
    type: "GeneralFilterFunction",
    subMenu: [
      {
        label: "基本配置",
        key: "filterOperatorSetting",
        type: "MenuItem",
        icon: "basicConfig",
        component: filterOperatorSetting,
      },
      {
        label: "数据预览",
        key: "filterDataPreview",
        type: "MenuItem",
        icon: "dataPreview",
        component: filterDataPreview,
      },
    ],
  },
  {
    // 节点类型：字段设置算子
    type: "FieldSettingFunction",
    subMenu: [
      {
        label: "基本配置",
        key: "calcFieldSetting",
        type: "MenuItem",
        icon: "basicConfig",
        component: calcFieldSetting,
      },
      {
        label: "数据预览",
        key: "calcDataPreview",
        type: "MenuItem",
        icon: "dataPreview",
        component: calcDataPreview,
      },
    ],
  },
  {
    // 节点类型：异常处理算子
    type: "ExceptionHandlingFunction",
    subMenu: [
      {
        label: "基本配置",
        key: "exceptionHandlerSetting",
        type: "MenuItem",
        icon: "basicConfig",
        component: exceptionHandlerSetting,
      },
      {
        label: "处理后字段",
        key: "exceptionHandlerField",
        type: "MenuItem",
        icon: "mergedField",
        component: exceptionHandlerField,
      },
      {
        label: "数据预览",
        key: "exceptionDataPreview",
        type: "MenuItem",
        icon: "dataPreview",
        component: exceptionDataPreview,
      },
    ],
  },
  {
    // 节点类型：redis输出节点
    type: "CommonRedisSink",
    subMenu: [
      {
        label: "基本配置",
        key: "redisSinkSetting",
        type: "MenuItem",
        icon: "basicConfig",
        component: redisBusinessSetting,
      },
      // {
      //   label: "数据预览",
      //   key: "redisDataPreview",
      //   type: "MenuItem",
      //   icon: "dataPreview",
      //   component: redisDataPreview,
      // },
    ],
  },
  {
    // 节点类型：模型输出节点
    type: "CommonJdbcSink",
    subMenu: [
      {
        label: "基本配置",
        key: "modelOutputSetting",
        type: "MenuItem",
        icon: "basicConfig",
        component: modelOutputSetting,
      },
      // {
      //   label: "数据预览",
      //   key: "modelDataPreview",
      //   type: "MenuItem",
      //   icon: "dataPreview",
      //   component: modelDataPreview,
      // },
    ],
  },
];

// 流程图设计页面--左侧节点参数
export const nodeTypeLabelMapTest: ComputerOptionsParamTest[] = [
  {
    title: "输入算子",
    type: "source",
    children: [
      {
        key: "source",
        value: "DatalogDataStreamSource",
        label: "定时记录输入算子",
        dataStream: "stream",
        icon: "DatalogDataStreamSource",
      },
      {
        key: "source",
        value: "CfgSource",
        label: "业务维度配置输入算子",
        dataStream: "map",
        icon: "CfgSource",
      },
      {
        key: "source",
        value: "ModelDataStreamSource",
        label: "模型数据输入算子",
        dataStream: "stream",
        icon: "DatalogDataStreamSource",
      },
    ],
  },
  {
    title: "数据运算算子",
    type: "trans",
    children: [
      {
        key: "trans",
        value: "StreamJoinFunction",
        label: "join连接算子",
        dataStream: "stream",
        icon: "StreamJoinFunction",
      },
      {
        key: "trans",
        value: "StreamGroupByFunction",
        label: "时序分组汇总算子",
        dataStream: "stream",
        icon: "StreamGroupByFunction",
      },
      {
        key: "trans",
        value: "GeneralFilterFunction",
        label: "筛选算子",
        dataStream: "stream",
        icon: "GeneralFilterFunction",
      },
      {
        key: "trans",
        value: "FieldSettingFunction",
        label: "字段设置算子",
        dataStream: "stream",
        icon: "FieldSettingFunction",
      },
      {
        key: "trans",
        value: "ExceptionHandlingFunction",
        label: "递增数据异常处理算子",
        dataStream: "stream",
        icon: "FieldSettingFunction",
      },
    ],
  },
  {
    title: "输出算子",
    type: "sink",
    children: [
      {
        key: "sink",
        value: "CommonRedisSink",
        label: "redis输出算子",
        icon: "CommonRedisSink",
      },
      {
        key: "sink",
        value: "CommonJdbcSink",
        label: "模型输出算子",
        icon: "CommonJdbcSink",
      },
    ],
  },
];

// 流程图设计页面--顶部连接线类型参数

export const showLineType: boolean = false;

export const PATH_OPTIONS: OptionsParam[] = [
  {
    label: "流程线",
    value: "step",
  },
  {
    label: "直线",
    value: "straight",
  },
  {
    label: "曲线",
    value: "default",
  },
];

// 流程图设计页面 -- 各个算子的来源连接线限制数据
export const sourceLimit: Record<string, number> = {
  StreamJoinFunction: 2,
  StreamGroupByFunction: 1,
  GeneralFilterFunction: 1,
  FieldSettingFunction: 1,
  CommonRedisSink: 1,
  CommonJdbcSink: 1,
};

// 流程图设计页面--流程图最大深度参数
export const maxDepth: number = 20;

export const maxNodeNum: number = 50;

// 流程图设计页面--所有算子中的表格字段头部定义
export const fieldTableHeader: FilterTableParam[] = [
  {
    label: "序号",
    type: "index",
    width: "62",
  },
  {
    prop: "field",
    label: "字段ID",
    minWidth: "100",
  },
  {
    prop: "description",
    label: "中文说明",
    minWidth: "100",
  },
  {
    prop: "frontedType",
    label: "数据类型",
    minWidth: "100",
  },
];

// 流程设计页面 -- 筛选算子中的过滤方法对应不同的字段类型
export const filterMethodOptions: FilterConditionParam = {
  number: [
    {
      label: "等于",
      value: "eq",
      key: "==",
      componentName: "inputNumber",
    },
    {
      label: "不等于",
      value: "neq",
      key: "!=",
      componentName: "inputNumber",
    },
    {
      label: "大于",
      value: "gt",
      key: ">",
      componentName: "inputNumber",
    },
    {
      label: "大于等于",
      value: "gte",
      key: ">=",
      componentName: "inputNumber",
    },
    {
      label: "小于",
      value: "lt",
      key: "<",
      componentName: "inputNumber",
    },
    {
      label: "小于等于",
      value: "lte",
      key: "<=",
      componentName: "inputNumber",
    },
    {
      label: "选择范围",
      value: "range",
      key: "range",
      componentName: "inputRangeNumber",
    },
    {
      label: "为空",
      value: "null",
      key: "== nil",
      componentName: "",
    },
    {
      label: "不为空",
      value: "notNull",
      key: "!= nil",
      componentName: "",
    },
    {
      label: "等于任意一个",
      value: "in",
      key: "in",
      componentName: "inputNumberTag",
    },
    {
      label: "不等于任意一个",
      value: "notIn",
      key: "notIn",
      componentName: "inputNumberTag",
    },
  ],
  string: [
    {
      label: "包含",
      value: "contains",
      key: ".contains",
      componentName: "input",
    },
    {
      label: "不包含",
      value: "notContains",
      key: "notContains",
      componentName: "input",
    },
    {
      label: "为空",
      value: "null",
      key: "== nil",
      componentName: "",
    },
    {
      label: "不为空",
      value: "notNull",
      key: "!= nil",
      componentName: "",
    },
    {
      label: "等于任意一个",
      value: "in",
      key: "in",
      componentName: "inputTag",
    },
    {
      label: "不等于任意一个",
      value: "notIn",
      key: "notIn",
      componentName: "inputTag",
    },
  ],
  date: [
    {
      label: "等于",
      value: "eq",
      key: "==",
      componentName: "datapicker",
    },
    {
      label: "不等于",
      value: "neq",
      key: "!=",
      componentName: "datapicker",
    },
    {
      label: "大于等于",
      value: "gte",
      key: ">=",
      componentName: "datapicker",
    },
    {
      label: "小于等于",
      value: "lte",
      key: "<=",
      componentName: "datapicker",
    },
    {
      label: "选择范围",
      value: "range",
      key: "range",
      componentName: "datetimerange",
    },
    {
      label: "为空",
      value: "null",
      key: "== nil",
      componentName: "",
    },
    {
      label: "不为空",
      value: "notNull",
      key: "!= nil",
      componentName: "",
    },
  ],
  enum: [
    {
      label: "等于",
      value: "eq",
      key: "==",
      componentName: "select",
    },
    {
      label: "不等于",
      value: "neq",
      key: "!=",
      componentName: "select",
    },
    {
      label: "等于任意一个",
      value: "in",
      key: "in",
      componentName: "multipleSelect",
    },
    {
      label: "不等于任意一个",
      value: "notIn",
      key: "notIn",
      componentName: "multipleSelect",
    },
    {
      label: "为空",
      value: "null",
      key: "== nil",
      componentName: "",
    },
    {
      label: "不为空",
      value: "notNull",
      key: "!= nil",
      componentName: "",
    },
  ],
};

// 流程设计页面 -- 筛选算子中的过滤方法对应的关键字
export const filterMethodKeys: Record<string, string> = {
  eq: "==",
  neq: "!=",
  gt: ">",
  gte: ">=",
  lt: "<",
  lte: "<=",
  null: "== nil",
  notNull: "!= nil",
};

// 分组配置页面 -- 常见指标枚举定义
export interface CommonMetricParam {
  id: number;
  key: string;
  label: string;
  dataType: string;
}

export const COMMON_METRICS: CommonMetricParam[] = [
  {
    id: 4,
    key: "vMin",
    label: "最小值",
    dataType: "double"
  },
  {
    id: 3,
    key: "vMax",
    label: "最大值",
    dataType: "double"
  },
  {
    id: 11,
    key: "vAvg",
    label: "平均值",
    dataType: "double"
  },
  {
    id: 10,
    key: "vSum",
    label: "累加值",
    dataType: "double"
  },
  {
    id: 6,
    key: "vCp95",
    label: "95百分位值",
    dataType: "double"
  },
  {
    id: 17,
    key: "vCount",
    label: "计数",
    dataType: "double"
  }
];

// 分组类型枚举
export const GROUP_TYPE_OPTIONS: OptionsParam[] = [
  {
    value: "periodIteration",
    label: "周期迭代计算"
  },
  {
    value: "groupAggregation",
    label: "时间分组聚合"
  }
];

// 筛选算子--两输入框范围组件
import ComTwoInput from "@/views/flowChart/components/ComTwoInput/index.vue";

// 筛选算子 -- tagInput标签输入框组件
import ComTagInput from "@/views/flowChart/components/ComTagInput/index.vue";

import { InputNumber as TInputNumber } from "tdesign-vue-next";

// 流程设计页面 -- 筛选算子中的过滤值所对应的组件类型
export const filterValueOptions: FilterValueOptionsParam = {
  input: {
    component: ElInput,
    props: {
      placeholder: "请输入文本内容",
      maxlength: 20,
    },
  },
  inputNumber: {
    component: TInputNumber,
    props: {
      placeholder: "请输入内容",
      theme: "normal",
      style: "width: 100%",
      align: "left",
      decimalPlaces: 6,
      min: "-99999999999.999999",
      max: "99999999999.999999",
      largeNumber: true,
      allowInputOverLimit: false,
    },
  },
  inputRangeNumber: {
    component: ComTwoInput,
    props: {
      placeholder: "请输入内容",
      theme: "normal",
      style: "width: 100%",
      decimalPlaces: 6,
      min: "-99999999999.999999",
      max: "99999999999.999999",
      largeNumber: true,
      allowInputOverLimit: false,
    },
  },
  inputNumberTag: {
    component: ComTagInput,
    props: {
      placeholder: "请输入内容",
      clearable: true,
      minCollapsedNum: 3,
      multiple: true,
      excessTagsDisplayType: "scroll",
      inputType: "number",
    },
  },
  inputTag: {
    component: ComTagInput,
    props: {
      placeholder: "请输入内容",
      clearable: true,
      minCollapsedNum: 3,
      multiple: true,
      excessTagsDisplayType: "scroll",
    },
  },
  datapicker: {
    component: ElDatePicker,
    props: {
      type: "datetime",
      placeholder: "选择日期",
      style: "width: 100%",
      valueFormat: "x",
    },
  },
  datetimerange: {
    component: ElDatePicker,
    props: {
      type: "datetimerange",
      placeholder: "选择日期",
      style: "width: 94%",
      valueFormat: "x",
    },
  },
  select: {
    component: ElSelect,
    props: {
      placeholder: "请选择",
      style: "width: 100%",
      key: "select",
    },
  },
  multipleSelect: {
    component: ElSelect,
    props: {
      placeholder: "请选择",
      style: "width: 100%",
      multiple: true,
      collapseTags: true,
      collapseTagsTooltip: true,
      maxCollapseTags: 2,
      // 定义key处理单选切换多选的问题
      key: "multipleSelect",
    },
  },
};
