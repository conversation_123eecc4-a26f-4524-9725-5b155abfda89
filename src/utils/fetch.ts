import axios, {
  type InternalAxiosRequestConfig,
  type AxiosResponse,
} from "axios";

// 导入loading组件
import { showLoading, hideLoading } from "@/utils/loading";

// 创建axios
const $http = axios.create({
  //设置默认请求地址
  baseURL: "",
  //设置请求超时时间
  timeout: 10000,
  //设置请求头
  headers: { "Content-Type": "application/json;charset=utf-8" },
});

// 请求拦截器
$http.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 显示loading
    showLoading({
      lock: true,
      fullscreen: true,
      text: "请求中",
      background: "rgba(0, 0, 0, 0.7)",
      customClass: "loading-class",
    });
    // 验证 token
    const token = localStorage.getItem("token") || "";
    if (config.headers != undefined) config.headers.Authorization = token;
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

//响应拦截
$http.interceptors.response.use(
  (response: AxiosResponse) => {
    hideLoading();
    // 响应数据为任意二进制流处理(文件下载)
    if (response.headers["content-type"] === "application/octet-stream") {
      return response;
    }
    // 状态码为200正常返回
    const { code, msg } = response.data;
    if (code === "00000" || code === 0) {
      return response.data;
    }
    ElMessage.error(msg || "error");
    return Promise.reject(new Error(msg || "Error"));
  },
  (error) => {
    hideLoading();
    ElMessage.error(error.message);
    return Promise.reject(error);
  }
);

// 导出封装的axios
export default $http;
