# 增加变量输入框需求

## 需求描述

### 1.用户需求：在 `recalculateDialog.vue` 中，原配置页面![img.png](img.png)，现把配置页面修改为![img_2.png](img_2.png)。
具体修改逻辑如下：
（1）增加流程变量，非必选。
（2）它的表头是 变量名称，变量标识，值；下面可增加一行，用户自己输入值进行，也可以删除这一行。
（3）变量名称对应 name, 变量标识对应 label, 值对应 value, 它们对应的值都是字符串类型，可以有多个。
    格式和字段如下：
    "variables": [
        {
        "name": "租户id",
        "label": "tenantId",
        "value": "1"
        }
    ]

    #输出定义
    {
        "resource": {
            "endTime": 1755820800000,
            "jobName": "test_group",
            "startTime": 1755129600000,
            "jobmanager": "1024M",
            "parallelism": 1,
            "taskmanager": "1024M",
            "variables": [
                {
                    "name": "租户id",
                    "label": "tenantId",
                    "value": "1"
                }
            ]
        }
    }    

### 2.用户需求：在 `basicAttribute.vue` 中，原配置页面![img_3.png](img_3.png)，现把配置页面修改为![img_4.png](img_4.png)。
具体修改逻辑如下：
（1）增加流程变量，非必选。
（2）它的表头是 变量名称，变量标识，值；下面可增加一行，用户自己输入值进行，也可以删除这一行。
（3）变量名称对应 name, 变量标识对应 label, 值对应 value, 它们对应的值都是字符串类型，可以有多个。
    格式和字段如下：
    "variables": [
        {
        "name": "租户id",
        "label": "tenantId",
        "value": "1"
        }
    ]

    #输出定义
    {
    "id": "T00002",
    "to": "T00002",
    "sql": "xxxx",
    "name": "业务维度配置输入算子2",
    "type": "CfgSource",
    "nodeType": "source",
    "dataStream": "batch",
    "modelLabel": "data-center-agg",
    "fieldsArray": {
        "path": "String",
        "rate": "double",
        "level": "int",
        "orgid": "long",
        "dataid": "long",
        "deviceid": "long",
        "orglabel": "String",
        "channelid": "long",
        "stationid": "long",
        "energytype": "int",
        "monitoredid": "long",
        "logicalindex": "int",
        "monitoredlabel": "String",
        "quantityobject_id": "long"
    },
    "variables": [
        {
            "name": "租户id",
            "label": "tenantId",
            "value": "1"
        }
    ]
}  



