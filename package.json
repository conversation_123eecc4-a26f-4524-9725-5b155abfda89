{"name": "flowchart-web", "private": true, "version": "1.0.77", "scripts": {"dev": "vite", "dev:clean": "npm run kill-port && npm run dev", "kill-port": "npx kill-port 9560", "build": "vue-tsc -b && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.5", "bignumber.js": "^9.1.2", "codemirror": "^5.65.18", "codemirror-editor-vue3": "^2.8.0", "element-plus": "^2.8.1", "lodash-es": "^4.17.21", "moment": "^2.30.1", "pinia": "^2.2.2", "vue": "^3.4.37", "vue-router": "^4.4.3"}, "devDependencies": {"@omega/cli-devops": "^1.4.4", "@types/codemirror": "^5.60.15", "@types/node": "^22.5.0", "@vitejs/plugin-vue": "^5.1.2", "@vue-flow/background": "^1.3.0", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.40.1", "@vue-flow/minimap": "^1.5.0", "@vue/test-utils": "^2.4.6", "ace-builds": "^1.36.2", "happy-dom": "^18.0.1", "kill-port": "^2.0.1", "sass": "^1.77.8", "sass-loader": "^16.0.1", "sortablejs": "^1.15.6", "sql-formatter": "^15.4.2", "tdesign-vue-next": "^1.10.3", "typescript": "^5.5.3", "unplugin-auto-import": "^0.18.2", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.1", "vitest": "^3.2.4", "vue-tsc": "^2.0.29", "vue3-ace-editor": "^2.2.4"}}