module.exports = {
  dockerReposServer: {
    // 镜像仓库名称
    repo_name: "lowcode/flowchart-web",
    host: "*************",
    user: {
      name: "dev",
      password: "Dev57611_Dev57611_Dev57611_Dev57611",
    },
    // 重要说明：
    // 版本号支持 2~4位版本号，以4位版本号为例 v1.3.1.{n}, {n}会根据镜像私库的近999条版本号进行排序，计算出其下一个版本号的{n}。
    // {n} 仅支持放在最后一位。
    // 如需要手动管理版本号只需要将{n}替换成想要的版本即可。例如v1.3.1.13代表我推送到镜像库的为v1.3.1.13版本
    // 详细见代码 ../dockerRepo.js
    tag: "v1.0.{n}",
    // docker仓库保存最近版本的镜像的最大数目
    image_max: 999,
  },
  dingDingRobot: {
    // 钉钉自定义机器人创建详细步骤: http://***********:4999/web/#/5?page_id=140
    // 钉钉自定义机器人
    robots: [
      // {
      //   secret:
      //     "SEC67e5754da290d8309e8505d37709ec9cd91cd50ef6f0d12606ba723a1480b1cc",
      //   webhook:
      //     "https://oapi.dingtalk.com/robot/send?access_token=5887aa2ccdedff05199d5a6ea49eaa6954ce06eae4e025534fdde50ea49ea449",
      // },
    ],
    // isAtAll: true,
    // 通过手机号@相关人
    // atMobiles: []
  },
  buildOption: {
    buildDir: "dist/lowcode",
    commond: "vite build --outDir {{buildDirPath}}",
  },
};
