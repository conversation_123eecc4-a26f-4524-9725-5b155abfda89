需求：

交互界面
![alt text](image.png)
![alt text](image-1.png)

功能要点：
1) 读取所有cet相关的日志点等级信息；
2) 设置批量设置日志点等级；
3) 模糊搜索日志点，不区分大小写。
4) 批量重置日志点（保存第一次查询到的日志点等级信息）。

查询所有任务管理器信息
GET http://************:18085/taskmanagers
结果示例：
{
    "taskmanagers": [
        {
            "id": "localhost:34021-00cc64",
            "path": "akka.tcp://flink@localhost:34021/user/rpc/taskmanager_0",
            "dataPort": 39673,
            "jmxPort": -1,
            "timeSinceLastHeartbeat": 1756107163454,
            "slotsNumber": 2,
            "freeSlots": 1,
            "totalResource": {
                "cpuCores": 2.0,
                "taskHeapMemory": 2192,
                "taskOffHeapMemory": 0,
                "managedMemory": 256,
                "networkMemory": 64,
                "extendedResources": {}
            },
            "freeResource": {
                "cpuCores": 1.0,
                "taskHeapMemory": 1096,
                "taskOffHeapMemory": 0,
                "managedMemory": 128,
                "networkMemory": 32,
                "extendedResources": {}
            },
            "hardware": {
                "cpuCores": 16,
                "physicalMemory": 33148792832,
                "freeMemory": 2432696320,
                "managedMemory": 268435456
            },
            "memoryConfiguration": {
                "frameworkHeap": 134217728,
                "taskHeap": 2298478592,
                "frameworkOffHeap": 134217728,
                "taskOffHeap": 0,
                "networkMemory": 67108864,
                "managedMemory": 268435456,
                "jvmMetaspace": 268435456,
                "jvmOverhead": 201326592,
                "totalFlinkMemory": 2902458368,
                "totalProcessMemory": 3372220416
            }
        }
    ]
}

注意：其中"id": "localhost:34021-00cc64" 用于其他接口的入参。

查询所有日志点的等级
GET http://************:18085/taskmanagers/{任务管理器id}/loggers
结果示例：
{
    "loggerLevelInfos": [
        {
            "loggerName": "ROOT",
            "logLevel": "INFO"
        },
        {
            "loggerName": "akka",
            "logLevel": "INFO"
        },
        {
            "loggerName": "akka.event",
            "logLevel": "INFO"
        },
        {
            "loggerName": "akka.event.slf4j",
            "logLevel": "INFO"
        },
        {
            "loggerName": "akka.event.slf4j.Slf4jLogger",
            "logLevel": "INFO"
        },
        {
            "loggerName": "akka.remote",
            "logLevel": "INFO"
        },
        {
            "loggerName": "akka.remote.RemoteActorRefProvider",
            "logLevel": "INFO"
        },
        {
            "loggerName": "akka.remote.Remoting",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.pool",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.pool.DruidAbstractDataSource",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.pool.DruidConnectionHolder",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.pool.DruidDataSource",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.pool.DruidDataSourceFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.pool.DruidDataSourceStatLoggerImpl",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.pool.DruidPooledConnection",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.pool.DruidPooledPreparedStatement",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.pool.DruidPooledStatement",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.stat",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.stat.DruidDataSourceStatManager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.stat.DruidStatService",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.stat.JdbcDataSourceStat",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.support",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.support.logging",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.support.logging.LogFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.support.logging.SLF4JImpl",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.util",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.util.JdbcUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.alibaba.druid.util.StringUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.config",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.config.ApplicationConfigManager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.config.EncryptionSvr",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.config.EngineConfigManager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.model",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.model.KeyedRowProcessConfig",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.message",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.message.MessageDispatcher",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.sink",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.sink.RedisMetaInfo",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.sink.RowsRedisSink",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.source",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.source.ObjectCfg",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.source.util",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.source.util.Singleton",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.transform",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.transform.GroupProcessFunction",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.transform.StreamGroupByFunction",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.transform.group",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.transform.group.CP95Algorithm",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.transform.group.ComputeExecutor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.transform.group.HistoryDataManager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.transform.group.IterationComputeNode",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.transform.group.PeriodIterationDataCache",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.plugin.base.flink.transform.group.RecalcDataReader",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.utils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.engine.utils.RowUtil",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.component",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.component.source",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.component.source.StationChannelDeviceCfg",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.component.source.multi_station_datalog",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.component.source.multi_station_datalog.MultiRealTimeDataReader",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.component.source.multi_station_datalog.MultiRecalcDatalogReader",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.component.source.multi_station_datalog.MultiStreamDatalogReaderSource",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.component.source.recalc",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.component.source.recalc.RecalcInfoManager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.thirdpart",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.thirdpart.JsonTransferUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.thirdpart.Result",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.util",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.util.ConfigJdbc",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.util.ConfigSingleton",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.util.HttpUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.util.JdbcUtil",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.util.PublicFunc",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.util.RedisUtil",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.util.Singleton",
            "logLevel": "INFO"
        },
        {
            "loggerName": "com.cet.fbp.common.util.TimeFunc",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.calcite",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.calcite.avatica",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.calcite.avatica.remote",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.calcite.avatica.remote.Driver",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api.common",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api.common.state",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api.common.state.StateDescriptor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api.java",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api.java.typeutils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api.java.typeutils.TypeExtractor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api.java.typeutils.runtime",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api.java.typeutils.runtime.PojoSerializerSnapshotData",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api.java.typeutils.runtime.kryo",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api.java.typeutils.runtime.kryo.KryoSerializer",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api.java.typeutils.runtime.kryo.KryoSerializerSnapshot",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.api.java.typeutils.runtime.kryo.KryoSerializerSnapshotData",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.configuration",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.configuration.Configuration",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.configuration.GlobalConfiguration",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming.state",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming.state.RocksDBKeyedStateBackend",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming.state.RocksDBKeyedStateBackendBuilder",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming.state.RocksDBMapState",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming.state.RocksDBOperationUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming.state.RocksDBResourceContainer",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming.state.restore",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming.state.restore.RocksDBHandle",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming.state.snapshot",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming.state.snapshot.RocksDBSnapshotStrategyBase",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.contrib.streaming.state.snapshot.RocksIncrementalSnapshotStrategy",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.core",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.core.fs",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.core.fs.FileSystem",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.core.fs.SafetyNetCloseableRegistry",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.core.memory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.core.memory.MemorySegmentFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.core.plugin",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.core.plugin.PluginConfig",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.core.plugin.PluginLoader",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.core.security",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.core.security.FlinkSecurityManager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.management",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.management.jmx",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.management.jmx.JMXService",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.metrics",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.metrics.MetricGroup",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.accumulators",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.accumulators.AccumulatorRegistry",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.blob",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.blob.BlobCacheSizeTracker",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.blob.BlobClient",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.blob.BlobOutputStream",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.blob.PermanentBlobCache",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.blob.TransientBlobCache",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.checkpoint",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.checkpoint.OperatorSubtaskState",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.entrypoint",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.entrypoint.ClusterEntrypointUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.execution",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.execution.librarycache",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.execution.librarycache.BlobLibraryCacheManager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.execution.librarycache.FlinkUserCodeClassLoaders",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.execution.librarycache.FlinkUserCodeClassLoaders$SafetyNetWrapperClassLoader",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.externalresource",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.externalresource.ExternalResourceUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.filecache",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.filecache.FileCache",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.fs",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.fs.hdfs",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.fs.hdfs.HadoopFsFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.fs.maprfs",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.fs.maprfs.MapRFsFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.heartbeat",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.heartbeat.HeartbeatMonitorImpl",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.disk",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.disk.BatchShuffleReadBufferPool",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.disk.FileChannelManagerImpl",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.disk.iomanager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.disk.iomanager.IOManager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.NettyShuffleEnvironment",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.NettyShuffleServiceFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.TaskEventDispatcher",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.api",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.api.serialization",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.api.serialization.SpanningWrapper",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.api.writer",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.api.writer.RecordWriter",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.buffer",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.buffer.LocalBufferPool",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.buffer.NetworkBufferPool",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.logger",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.logger.NetworkActionsLogger",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.netty",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.netty.NettyBufferPool",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.netty.NettyClient",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.netty.NettyConfig",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.netty.NettyServer",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.netty.PartitionRequestClientFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.partition",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.partition.PipelinedSubpartition",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.partition.ResultPartition",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.partition.ResultPartitionFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.partition.ResultPartitionManager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.partition.consumer",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.partition.consumer.ChannelStatePersister",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.partition.consumer.LocalInputChannel",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.partition.consumer.RecoveredInputChannel",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.partition.consumer.SingleInputGate",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.io.network.partition.consumer.SingleInputGateFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.memory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.memory.MemoryManager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.metrics",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.metrics.MetricRegistryConfiguration",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.metrics.MetricRegistryImpl",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.metrics.ReporterSetup",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.metrics.dump",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.metrics.dump.MetricDumpSerialization",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.metrics.dump.MetricQueryService",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.metrics.util",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.metrics.util.MetricUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.rpc",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.rpc.MainThreadValidatorUtil",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.rpc.akka",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.rpc.akka.AkkaInvocationHandler",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.rpc.akka.AkkaRpcActor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.rpc.akka.AkkaRpcService",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.rpc.akka.AkkaRpcServiceUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.rpc.akka.AkkaUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.rpc.akka.CleanupOnCloseRpcSystem",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.rpc.akka.DeadLettersActor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.rpc.akka.SupervisorActor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.security",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.security.DynamicConfiguration",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.security.KerberosUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.security.SecurityUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.security.contexts",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.security.contexts.HadoopSecurityContextFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.security.modules",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.security.modules.HadoopModule",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.security.modules.HadoopModuleFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.security.modules.JaasModule",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.AsyncSnapshotCallable",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.CheckpointStreamWithResultProvider",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.DefaultOperatorStateBackend",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.IncrementalRemoteKeyedStateHandle",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.LocalRecoveryDirectoryProviderImpl",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.SnapshotStrategyRunner",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.StateBackendLoader",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.StateUtil",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.TaskExecutorLocalStateStoresManager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.TaskExecutorStateChangelogStoragesManager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.TaskStateManagerImpl",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.changelog",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.changelog.StateChangelogStorageLoader",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.filesystem",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.state.filesystem.FsCheckpointStreamFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor.DefaultJobLeaderService",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor.KvStateService",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor.TaskExecutor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor.TaskExecutorResourceUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor.TaskManagerConfiguration",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor.TaskManagerRunner",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor.TaskManagerServices",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor.rpc",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor.rpc.RpcResultPartitionConsumableNotifier",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor.slot",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor.slot.TaskSlot",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskexecutor.slot.TaskSlotTableImpl",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskmanager",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskmanager.NettyShuffleEnvironmentConfiguration",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.taskmanager.Task",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.util",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.util.ClusterUncaughtExceptionHandler",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.util.ConfigurationParserUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.util.EnvironmentInformation",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.util.HadoopUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.util.Hardware",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.util.config",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.util.config.memory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.runtime.util.config.memory.ManagedMemoryUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.bootstrap",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.bootstrap.Bootstrap",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.bootstrap.ServerBootstrap",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.buffer",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.buffer.AbstractByteBuf",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.buffer.ByteBufUtil",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.buffer.PoolThreadCache",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.buffer.PooledByteBufAllocator",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.AbstractChannel",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.AbstractChannelHandlerContext",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.ChannelHandlerMask",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.ChannelInitializer",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.ChannelOutboundBuffer",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.DefaultChannelId",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.DefaultChannelPipeline",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.MultithreadEventLoopGroup",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.nio",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.nio.AbstractNioChannel",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.nio.NioEventLoop",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.socket",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.socket.nio",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.channel.socket.nio.NioServerSocketChannel",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.resolver",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.resolver.AddressResolverGroup",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.NetUtil",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.NetUtilInitializations",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.ResourceLeakDetector",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.ResourceLeakDetectorFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.concurrent",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.concurrent.AbstractEventExecutor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.concurrent.DefaultPromise",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.concurrent.DefaultPromise.rejectedExecution",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.concurrent.GlobalEventExecutor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.concurrent.SingleThreadEventExecutor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.internal",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.internal.CleanerJava6",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.internal.InternalThreadLocalMap",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.internal.MacAddressUtil",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.internal.PlatformDependent",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.internal.PlatformDependent0",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.internal.SystemPropertyUtil",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.internal.logging",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.netty4.io.netty.util.internal.logging.InternalLoggerFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.shaded.zookeeper3",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.api",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.api.operators",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.api.operators.AbstractStreamOperator",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.api.operators.BackendRestorerProcedure",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.api.operators.InternalTimeServiceManagerImpl",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.api.operators.StreamOperatorStateHandler",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.api.operators.StreamTaskStateInitializerImpl",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.io",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.io.StreamOneInputProcessor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.io.checkpointing",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.io.checkpointing.CheckpointedInputGate",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.io.checkpointing.SingleCheckpointBarrierHandler",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.tasks",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.tasks.AsyncCheckpointRunnable",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.tasks.ChainingOutput",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.tasks.OperatorChain",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.tasks.RegularOperatorChain",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.tasks.StreamTask",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.tasks.SubtaskCheckpointCoordinatorImpl",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.tasks.SystemProcessingTimeService",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.tasks.mailbox",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.table",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.table.runtime",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.table.runtime.generated",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.table.runtime.generated.CompileUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.table.runtime.generated.GeneratedClass",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.util",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.util.FatalExitExceptionHandler",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.util.InstantiationUtil",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.flink.util.NetUtils",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.hadoop",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.hadoop.metrics2",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.hadoop.metrics2.lib",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.hadoop.metrics2.lib.Interns",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.hadoop.security",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.hadoop.security.UserGroupInformation",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.hadoop.security.authentication",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.hadoop.security.authentication.util",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.hadoop.security.authentication.util.KerberosName",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.hadoop.util",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.hadoop.util.PerformanceAdvisory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.hadoop.util.Shell",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.kafka",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.apache.zookeeper",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.CompleteChannelFuture",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.DefaultChannelFuture",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.DefaultChannelPipeline",
            "logLevel": "OFF"
        },
        {
            "loggerName": "org.jboss.netty.channel.SimpleChannelHandler",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.SimpleChannelUpstreamHandler",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.socket",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.socket.nio",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.socket.nio.AbstractNioBossPool",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.socket.nio.AbstractNioSelector",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.socket.nio.AbstractNioWorkerPool",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.socket.nio.DefaultNioSocketChannelConfig",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.socket.nio.NioClientSocketChannel",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.socket.nio.NioClientSocketPipelineSink",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.socket.nio.NioServerSocketChannel",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.channel.socket.nio.SelectorUtil",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.util",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.util.HashedWheelTimer",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.util.ThreadRenamingRunnable",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.util.internal",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.util.internal.SharedResourceMisuseDetector",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.jboss.netty.util.internal.SystemPropertyUtil",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.rocksdb",
            "logLevel": "INFO"
        },
        {
            "loggerName": "org.rocksdb.FlinkCompactionFilter",
            "logLevel": "INFO"
        },
        {
            "loggerName": "redis",
            "logLevel": "INFO"
        },
        {
            "loggerName": "redis.clients",
            "logLevel": "INFO"
        },
        {
            "loggerName": "redis.clients.jedis",
            "logLevel": "INFO"
        },
        {
            "loggerName": "redis.clients.jedis.HostAndPort",
            "logLevel": "INFO"
        },
        {
            "loggerName": "redis.clients.jedis.JedisFactory",
            "logLevel": "INFO"
        },
        {
            "loggerName": "redis.clients.jedis.JedisPool",
            "logLevel": "INFO"
        }
    ]
}
注意：里面包含了flink和cet的， 我们值关注cet相关的。

批量设置日志点等级

POST http://************:18085/taskmanagers/{任务管理器id}/updateLoggersLevel
body示例：
{
    "loggerLevelInfos":[
        {
            "loggerName": "com.cet.electric",
            "logLevel": "INFO"
        }
    ]
}
